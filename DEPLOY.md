# Contact connector

## Local

### Prerequisites

In order for this application to work, you require to have a RabbitMQ
docker images running

Keep in mind that we need to export specific ports in order for the application
to communicate with the docker image

#### Run RabbitMQ

```bash
docker pull rabbitmq
docker run -d --hostname rabbit --name rabbit -p 15672:15672 -p 5672:5672 rabbitmq:3-management
```

> Here we can change the port forwarding as we desire. Forwared ports should be configured
> in [application.properties](./workflow-connectors\contact-workflow-connector/src/main/resources/application.properties)
> or in [local.env](./envs/local.env)

### How to Start

Since it is a standard Spring boot application, we can start it same as the rest of applications
> make sure to add the env file in the `Run Configuration`

### How to test

In order to test the connector we need to mock the rabbitmq messages.
This can be done with the [workflow-mock](https://git.itsf.io/galaxion/oss-monaco/workflow-engine/workflow-mock/-/tree/master) project

In order to find out how to initialise workflow-mock please refer to its [README file](https://git.itsf.io/galaxion/oss-monaco/workflow-engine/workflow-mock/-/blob/master/README.md)

## CI

The deployment in CI is handled automatically thanks to the [charts-templates pipeline](https://gitlab.prod.lan/it-factory/charts-templates/-/pipelines)

## Dependencies

- [RabbitMQ](https://hub.docker.com/_/rabbitmq/)