package com.millicom.workflow.connector.mappers;

import com.millicom.workflow.spi.com.dto.v1.AddressDTO;
import com.millicom.workflow.spi.com.dto.v1.enumeration.AddressTypeEnum;
import com.millicom.workflow.connector.contact.client.domain.request.CreateAddressRequestDTO;
import com.millicom.workflow.connector.contact.mappers.AddressMapper;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

public class AddressMapperTest {

    @Test
    void toCreateRequest_shouldBuildStreetWhenBilling() {
        AddressDTO address = new AddressDTO();
        address.setType(AddressTypeEnum.BILLING);
        address.setStreet("Calle Principal");
        address.setStreetNumber("123");
        address.setAddressLine1("Zona 1");
        address.setAddressLine2("Ciudad");        
        address.setAddressLine3("Casa 2");
        address.setTown("Guatemala");
        address.setArea("Barrio Centro");
        address.setPoBox("PA-PUNTA-PACIFICA");
        address.setBuildingType("ASFTH");


        CreateAddressRequestDTO dto = AddressMapper.toCreateRequest(address);

        assertNotNull(dto);
        assertTrue(dto.getStreet().contains("Ciudad Guatemala Zona 1 Barrio Centro Calle Principal Casa 2"));
        assertEquals("123", dto.getStreetNumber());
    }

    @Test
    void toCreateRequest_shouldReturnEmptyDtoWhenAddressIsNull() {
        CreateAddressRequestDTO dto = AddressMapper.toCreateRequest(null);
        assertNotNull(dto);
        assertNull(dto.getStreet());
    }
}
