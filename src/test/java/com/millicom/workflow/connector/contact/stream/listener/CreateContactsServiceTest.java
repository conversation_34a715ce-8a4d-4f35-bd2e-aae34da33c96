package com.millicom.workflow.connector.contact.stream.listener;

import org.apache.tomcat.jni.Local;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

import com.millicom.workflow.connector.contact.client.domain.dto.CommunicationPreferenceDTO;
import com.millicom.workflow.connector.contact.client.domain.request.ContactRequestDTO;
import com.millicom.workflow.connector.contact.client.domain.request.CreateAddressRequestDTO;
import com.millicom.workflow.connector.contact.client.domain.request.PermissionGroupRequestDTO;
import com.millicom.workflow.connector.contact.client.domain.request.PermissionRequestDTO;
import com.millicom.workflow.connector.contact.domain.dto.ContactAddressDTO;
import com.millicom.workflow.connector.contact.domain.dto.CustomerCommunicationSettingsDTO;
import com.millicom.workflow.connector.contact.domain.dto.CustomerIdentityDocumentDTO;
import com.millicom.workflow.connector.contact.domain.dto.InvoiceSettingsDTO;
import com.millicom.workflow.connector.contact.domain.dto.PersonDTO;
import com.millicom.workflow.connector.contact.domain.request.CreateContactsRequest;
import com.millicom.workflow.connector.contact.enums.CommunicationChannelEnum;
import com.millicom.workflow.connector.contact.enums.PermissionEnum;
import com.millicom.workflow.connector.contact.enums.PermissionGroupEnum;
import com.millicom.workflow.connector.contact.service.ContactCreationService;
import com.millicom.workflow.spi.com.dto.v1.AddressDTO;
import com.millicom.workflow.spi.com.dto.v1.ContactDTO;
import com.millicom.workflow.spi.com.dto.v1.EmailDTO;
import com.millicom.workflow.spi.com.dto.v1.IdentityDocumentDTO;
import com.millicom.workflow.spi.com.dto.v1.PhoneNumberDTO;
import com.millicom.workflow.spi.com.dto.v1.enumeration.AddressTypeEnum;

import util.Utils;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Collections;
import java.util.EnumSet;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.stream.Collectors;


@ExtendWith(MockitoExtension.class)
class CreateContactsServiceTest {


    @InjectMocks
    private ContactCreationService createContactService;

    @Test
    void test_getOwnerCreateContactRequest_pass() {
        // GIVEN
        EmailDTO email = Utils.createEmailDTO();
        PhoneNumberDTO number = Utils.createPhoneNumberDTO();
        AddressDTO address = Utils.createContactAddressDTO();
        IdentityDocumentDTO id = new IdentityDocumentDTO(1L, true, "123456", "CC", "Colombia", LocalDate.MAX);
        ContactDTO contact = Utils.createContact(email, number, address, id);
        
        // WHEN
        ContactRequestDTO result = createContactService.getCreateContactRequest(contact);

        // THEN
        Assertions.assertNotNull(result);
        Assertions.assertEquals(1, result.getAddresses().size());
        Assertions.assertEquals(AddressTypeEnum.BILLING, result.getAddresses().toArray(new CreateAddressRequestDTO[0])[0].getType());
        Assertions.assertEquals(1, result.getIdentityDocuments().size());
        Assertions.assertEquals(1, result.getEmails().size());
        Assertions.assertEquals(1, result.getPhoneNumbers().size());
    }

    /*
    @Test
    void test_getPayerCreateContactRequest_pass() {
        // GIVEN
        EmailDTO email = Utils.createEmailDTO();
        PhoneNumberDTO number = Utils.createPhoneNumberDTO();
        ContactAddressDTO address = Utils.createContactAddressDTO();
        CustomerIdentityDocumentDTO id = new CustomerIdentityDocumentDTO("personal id", "identifier", LocalDate.MAX, "BOSTWANAIS", true);
        PersonDTO person = new PersonDTO("test", "test", "Mr.", LocalDate.MIN, "EN");
        InvoiceSettingsDTO invoiceSettings = new InvoiceSettingsDTO("FR", "Me");
        ContactDTO payer = Utils.createContact(email, number, person, address, id);
        payer.setEmails(Collections.singleton(email));
        payer.setNumbers(Collections.singleton(number));
        payer.setAddresses(List.of(address));
        payer.setContactUuid("id");
        payer.setIdentityDocuments(Collections.singleton(id));
        payer.setPerson(person);
        CustomerCommunicationSettingsDTO settings = Utils.createCustomerCommunicationSettingsDTO();
        CreateContactsRequest createContactsRequest = new CreateContactsRequest();
        createContactsRequest.setCommunicationSettings(settings);
        createContactsRequest.setPayer(payer);
        createContactsRequest.setInvoiceSettings(invoiceSettings);
        // WHEN
        ContactRequestDTO result = createContactService.getPayerCreateContactRequest(createContactsRequest);
        // THEN
        Assertions.assertNotNull(result);
        Assertions.assertEquals(1, result.getAddresses().size());
        Assertions.assertEquals(AddressTypeEnum.BILLING, result.getAddresses().toArray(new CreateAddressRequestDTO[0])[0].getType());
        Assertions.assertEquals(1, result.getIdentityDocuments().size());
        Assertions.assertEquals(1, result.getEmails().size());
        Assertions.assertEquals(1, result.getPhoneNumbers().size());
    }

    @Test
    void test_getOwnerCreateContactRequest_samePayer_pass() {
        EmailDTO email = Utils.createEmailDTO();
        PhoneNumberDTO number = Utils.createPhoneNumberDTO();
        ContactAddressDTO payerAddress = new ContactAddressDTO(1L, "Mars", "payer street", "Asdf", null, null, null, "CO_CORK", AddressTypeEnum.BILLING, "1234", "123456", "country", "poBox", "area", "streetQualifier", "streetNumber", "buildingType", "flatNumber");
        ContactAddressDTO ownerAddress = new ContactAddressDTO(1L, "Mars", "owner street", "Asdf", null, null, null, "CO_CORK", AddressTypeEnum.DELIVERY, "1234", "123456", "country", "poBox", "area", "streetQualifier", "streetNumber", "buildingType", "flatNumber");
        CustomerIdentityDocumentDTO id = new CustomerIdentityDocumentDTO("personal id", "identifier", LocalDate.MAX, "BOSTWANAIS", true);
        PersonDTO person = new PersonDTO("test", "test", "Mr.", LocalDate.MIN, "EN");
        ContactDTO payer = new ContactDTO();
        payer.setEmails(Collections.singleton(email));
        payer.setNumbers(Collections.singleton(number));
        payer.setAddresses(List.of(payerAddress));
        payer.setContactUuid("id");
        payer.setIdentityDocuments(Collections.singleton(id));
        payer.setPerson(person);
        ContactDTO owner = new ContactDTO();
        owner.setEmails(Collections.singleton(email));
        owner.setNumbers(Collections.singleton(number));
        owner.setAddresses(List.of(ownerAddress));
        owner.setContactUuid("id");
        owner.setIdentityDocuments(Collections.singleton(id));
        owner.setPerson(person);
        CustomerCommunicationSettingsDTO settings = Utils.createCustomerCommunicationSettingsDTO();
        CreateContactsRequest createContactsRequest = new CreateContactsRequest();
        createContactsRequest.setCommunicationSettings(settings);
        createContactsRequest.setPayer(payer);
        createContactsRequest.setOwner(owner);
        getBillingAddress();

        ContactRequestDTO result = createContactService.getCreateContactRequest(createContactsRequest);

        Assertions.assertNotNull(result);
        Assertions.assertEquals(2, result.getAddresses().size());
        Assertions.assertTrue(result.getAddresses().stream().anyMatch(a -> "payer street".equals(a.getStreet()) && AddressTypeEnum.BILLING.equals(a.getType())));
        Assertions.assertTrue(result.getAddresses().stream().anyMatch(a -> "owner street".equals(a.getStreet()) && AddressTypeEnum.DELIVERY.equals(a.getType())));
        Assertions.assertEquals(1, result.getIdentityDocuments().size());
        Assertions.assertEquals(1, result.getEmails().size());
        Assertions.assertEquals(1, result.getPhoneNumbers().size());
    }

    @Test
    void test_getDefaultCommunicationSettings_MOBILE_pass() {
        CustomerCommunicationSettingsDTO settings = Utils.createCustomerCommunicationSettingsDTO();
        CreateContactsRequest createContactsRequest = new CreateContactsRequest();
        createContactsRequest.setCommunicationSettings(settings);
        CommunicationPreferenceDTO result = createContactService.getDefaultCommunicationSettings(createContactsRequest);

        Assertions.assertNotNull(result);
        Assertions.assertEquals(Locale.ENGLISH, result.getLanguage());
        Assertions.assertEquals(CommunicationChannelEnum.MOBILE_NUMBER, result.getChannel());
        Assertions.assertEquals("12345", result.getPhoneNumber());
        Assertions.assertEquals(Utils.EMAIL, result.getEmail());
    }

    @Test
    void test_getDefaultCommunicationSettings_EMAIL_pass() {
        CustomerCommunicationSettingsDTO settings = new CustomerCommunicationSettingsDTO(Utils.EMAIL, "12345", "EMAIL", "IRL", "EN");
        CreateContactsRequest createContactsRequest = new CreateContactsRequest();
        createContactsRequest.setCommunicationSettings(settings);
        CommunicationPreferenceDTO result = createContactService.getDefaultCommunicationSettings(createContactsRequest);

        Assertions.assertNotNull(result);
        Assertions.assertEquals(Locale.ENGLISH, result.getLanguage());
        Assertions.assertEquals(CommunicationChannelEnum.EMAIL, result.getChannel());
        Assertions.assertEquals("12345", result.getPhoneNumber());
        Assertions.assertEquals(Utils.EMAIL, result.getEmail());
    }

    @Test
    void test_getDefaultCommunicationSettings_SMS_pass() {
        CustomerCommunicationSettingsDTO settings = new CustomerCommunicationSettingsDTO(Utils.EMAIL, "12345", "SMS", "IRL", "EN");
        CreateContactsRequest createContactsRequest = new CreateContactsRequest();
        createContactsRequest.setCommunicationSettings(settings);
        CommunicationPreferenceDTO result = createContactService.getDefaultCommunicationSettings(createContactsRequest);

        Assertions.assertNotNull(result);
        Assertions.assertEquals(Locale.ENGLISH, result.getLanguage());
        Assertions.assertEquals(CommunicationChannelEnum.SMS, result.getChannel());
        Assertions.assertEquals("12345", result.getPhoneNumber());
        Assertions.assertEquals(Utils.EMAIL, result.getEmail());
    }

    @Test
    void test_getPayerCommunicationSettings_pass() {
        CustomerCommunicationSettingsDTO settings = new CustomerCommunicationSettingsDTO(Utils.EMAIL, "12345", "SMS", "FR", "FR");
        InvoiceSettingsDTO invoiceSettings = new InvoiceSettingsDTO("FR", "Me");
        CreateContactsRequest createContactsRequest = new CreateContactsRequest();
        createContactsRequest.setCommunicationSettings(settings);
        createContactsRequest.setInvoiceSettings(invoiceSettings);
        CommunicationPreferenceDTO result = createContactService.getDefaultCommunicationSettings(createContactsRequest);

        Assertions.assertNotNull(result);
        Assertions.assertEquals(Locale.FRENCH, result.getLanguage());
        Assertions.assertEquals(CommunicationChannelEnum.SMS, result.getChannel());
        Assertions.assertEquals("12345", result.getPhoneNumber());
        Assertions.assertEquals(Utils.EMAIL, result.getEmail());
    }

    @Test
    void test_areSamePerson_sameId_samePerson_pass() {
        PersonDTO ownerPerson = new PersonDTO("test", "test", "Mr.", LocalDate.MIN, "EN");
        CustomerIdentityDocumentDTO ownerId = new CustomerIdentityDocumentDTO("personal id", "identifier", LocalDate.MAX, "BOSTWANAIS" , true);
        ContactDTO owner = new ContactDTO();
        owner.setIdentityDocuments(Collections.singleton(ownerId));
        owner.setPerson(ownerPerson);

        PersonDTO payerPerson = new PersonDTO("test", "test", "Mr.", LocalDate.MIN, "EN");
        CustomerIdentityDocumentDTO personId = new CustomerIdentityDocumentDTO("personal id", "identifier", LocalDate.MAX, "BOSTWANAIS" , true);
        ContactDTO payer = new ContactDTO();
        payer.setIdentityDocuments(Collections.singleton(personId));
        payer.setPerson(payerPerson);

        Assertions.assertTrue(createContactService.areSamePerson(owner, payer));
    }

    @Test
    void test_areSamePerson_differentId_samePerson_doesnt_pass() {
        PersonDTO ownerPerson = new PersonDTO("owner", "test", "Mr.", LocalDate.MIN, "EN");
        CustomerIdentityDocumentDTO ownerId = new CustomerIdentityDocumentDTO("owner id", "identifier", LocalDate.MAX, "BOSTWANAIS" , true);
        ContactDTO owner = new ContactDTO();
        owner.setIdentityDocuments(Collections.singleton(ownerId));
        owner.setPerson(ownerPerson);

        PersonDTO payerPerson = new PersonDTO("payer", "test", "Mr.", LocalDate.MIN, "EN");
        CustomerIdentityDocumentDTO personId = new CustomerIdentityDocumentDTO("payer id", "identifier", LocalDate.MAX, "BOSTWANAIS" , true);
        ContactDTO payer = new ContactDTO();
        payer.setIdentityDocuments(Collections.singleton(personId));
        payer.setPerson(payerPerson);

        Assertions.assertFalse(createContactService.areSamePerson(owner, payer));
    }

    @Test
    void test_areSamePerson_noOwnerId_differentPerson_pass() {
        PersonDTO ownerPerson = new PersonDTO("test12", "test12", "Mr.12", LocalDate.MIN, "EN");
        ContactDTO owner = new ContactDTO();
        owner.setIdentityDocuments(Collections.emptySet());
        owner.setPerson(ownerPerson);

        PersonDTO payerPerson = new PersonDTO("test", "test", "Mr.", LocalDate.MIN, "EN");
        CustomerIdentityDocumentDTO personId = new CustomerIdentityDocumentDTO("personal id", "identifier", LocalDate.MAX, "BOSTWANAIS" , true);
        ContactDTO payer = new ContactDTO();
        payer.setIdentityDocuments(Collections.singleton(personId));
        payer.setPerson(payerPerson);

        Assertions.assertFalse(createContactService.areSamePerson(owner, payer));
    }

    @Test
    void test_areSamePerson_differentId_differentPerson_doesnt_pass() {
        PersonDTO ownerPerson = new PersonDTO("test12", "test12", "Mr.12", LocalDate.MIN, "EN");
        CustomerIdentityDocumentDTO ownerId = new CustomerIdentityDocumentDTO("owner id", "identifier", LocalDate.MAX, "WALLIS ET FOUTOUNA" , true);
        ContactDTO owner = new ContactDTO();
        owner.setIdentityDocuments(Collections.singleton(ownerId));
        owner.setPerson(ownerPerson);

        PersonDTO payerPerson = new PersonDTO("test", "test", "Mr.", LocalDate.MIN, "EN");
        CustomerIdentityDocumentDTO personId = new CustomerIdentityDocumentDTO("payer id", "identifier", LocalDate.MAX, "BOSTWANAIS" , true);
        ContactDTO payer = new ContactDTO();
        payer.setIdentityDocuments(Collections.singleton(personId));
        payer.setPerson(payerPerson);

        Assertions.assertFalse(createContactService.areSamePerson(owner, payer));
    }

    @Test
    void test_when_get_permissions_enabled_then_result_is_empty() {
        EmailDTO email = Utils.createEmailDTO();
        PhoneNumberDTO number = Utils.createPhoneNumberDTO();
        PersonDTO person = new PersonDTO("test", "test", "Mr.", LocalDate.MIN, "EN");
        ContactAddressDTO address = Utils.createContactAddressDTO();
        CustomerIdentityDocumentDTO id = new CustomerIdentityDocumentDTO("personal id", "identifier", LocalDate.MAX, "BOSTWANAIS" , true);

        ContactDTO contactDTO = Utils.createContact(email, number, person, address, id);

        List<PermissionGroupRequestDTO> permissionGroups = createContactService.getPermissionGroups(contactDTO, true);
        Assertions.assertTrue(permissionGroups.isEmpty());
    }

    @Test
    void test_when_get_permissions_enabled_then_result_is_ok() {
        EmailDTO email = Utils.createEmailDTO();
        PhoneNumberDTO number = Utils.createPhoneNumberDTO();
        PersonDTO person = new PersonDTO("test", "test", "Mr.", LocalDate.MIN, "EN");
        ContactAddressDTO address = Utils.createContactAddressDTO();
        CustomerIdentityDocumentDTO id = new CustomerIdentityDocumentDTO("personal id", "identifier", LocalDate.MAX, "BOSTWANAIS" , true);

        ContactDTO contactDTO = Utils.createContact(email, number, person, address, id);

        Map<PermissionGroupEnum, EnumSet<PermissionEnum>> enabledPermissions = new HashMap<>();
        EnumSet<PermissionEnum> permissionsEnabled = EnumSet.of(PermissionEnum.ALLOW_EMAIL_CONTACT, PermissionEnum.ALLOW_PHONE_CONTACT);
        enabledPermissions.put(PermissionGroupEnum.ACTIVE_CUSTOMER, permissionsEnabled);
        contactDTO.setEnabledPermissions(enabledPermissions);

        Map<PermissionGroupEnum, EnumSet<PermissionEnum>> disabledPermissions = new HashMap<>();
        EnumSet<PermissionEnum> permissionsDisabled = EnumSet.of(PermissionEnum.ALLOW_DIRECT_MAIL_CONTACT, PermissionEnum.ALLOW_FOTS_CONTACT);
        disabledPermissions.put(PermissionGroupEnum.ACTIVE_CUSTOMER, permissionsDisabled);
        contactDTO.setDisabledPermissions(disabledPermissions);

        List<PermissionGroupRequestDTO> permissionGroups = createContactService.getPermissionGroups(contactDTO, true);
        Assertions.assertEquals(enabledPermissions.size(), permissionGroups.size());
        Assertions.assertEquals(enabledPermissions.get(PermissionGroupEnum.ACTIVE_CUSTOMER).size(), permissionGroups.get(0).getPermissions().size());
        Assertions.assertEquals(new ArrayList<>(enabledPermissions.get(PermissionGroupEnum.ACTIVE_CUSTOMER)), permissionGroups.get(0).getPermissions().stream().map(PermissionRequestDTO::getPermission).collect(Collectors.toList()));
        permissionGroups.get(0).getPermissions().forEach(permission -> Assertions.assertTrue(permission.getEnabled()));
    }

    static CreateAddressRequestDTO getBillingAddress() {
        return getAddressReq(AddressTypeEnum.BILLING);
    }

    static CreateAddressRequestDTO getAddressReq(AddressTypeEnum type) {
        CreateAddressRequestDTO result = new CreateAddressRequestDTO();
        result.setType(type);
        return result;
    }
    */
}
