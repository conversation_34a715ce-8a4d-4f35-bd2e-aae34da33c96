package com.millicom.workflow.connector.contact.service;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import com.millicom.workflow.connector.contact.client.ContactClient;
import com.millicom.workflow.connector.contact.client.domain.request.ContactRequestDTO;
import com.millicom.workflow.connector.contact.domain.request.CreateContactRequest;
import com.millicom.workflow.connector.contact.service.ContactsCreationService;

import util.ResourceLoader;

import java.io.IOException;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class ContactsCreationServiceTest {

    @InjectMocks
    private ContactsCreationService contactCreationService;

    @Mock
    private ContactClient contactClient;
    private final ResourceLoader resourceLoader = new ResourceLoader();

    @Test
    void createContact_validContact_callCreateContact() throws IOException {
        // GIVEN
        var createContactsRequest = resourceLoader.loadResource("json/create-contact/valid-contact-request.json", CreateContactRequest.class);
        when(contactClient.createContact(any())).thenReturn("100");

        // WHEN
        var uuid = contactCreationService.createContact(createContactsRequest);

        // THEN
        var contactRequestCaptor = ArgumentCaptor.forClass(ContactRequestDTO.class);
        verify(contactClient).createContact(contactRequestCaptor.capture());
        var contactRequest = contactRequestCaptor.getValue();
        assertEquals("100", uuid);
        assertEquals("Jean", contactRequest.getFirstName());
        assertEquals("Michel", contactRequest.getLastName());
    }
}
