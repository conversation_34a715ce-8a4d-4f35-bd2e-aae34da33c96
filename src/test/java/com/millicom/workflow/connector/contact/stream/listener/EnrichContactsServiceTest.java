package com.millicom.workflow.connector.contact.stream.listener;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import com.millicom.workflow.connector.contact.client.ContactClient;
import com.millicom.workflow.connector.contact.client.domain.dto.CommunicationPreferenceDTO;
import com.millicom.workflow.connector.contact.client.domain.response.ContactResponseDTO;
import com.millicom.workflow.connector.contact.client.domain.response.permissions.PermissionGroupListResponseDTO;
import com.millicom.workflow.connector.contact.domain.dto.ContactDTO;
import com.millicom.workflow.connector.contact.domain.dto.CustomerCommunicationSettingsDTO;
import com.millicom.workflow.connector.contact.domain.dto.EmailDTO;
import com.millicom.workflow.connector.contact.service.EnrichContactService;
import com.millicom.workflow.spi.com.dto.v1.enumeration.TypePhoneNumberEnum;

import util.Utils;

import java.util.ArrayList;
import java.util.List;

@ExtendWith(MockitoExtension.class)
class EnrichContactsServiceTest {

    @InjectMocks
    private EnrichContactService enrichContactService;

    @Mock
    private ContactClient contactClient;


    @Test
    void test_mapEmails_pass() {
        var contact = new ContactDTO();
        ContactResponseDTO payer = Utils.createContactResponseDTO();
        enrichContactService.mapEmails(contact, payer);
        List<EmailDTO> mails = new ArrayList<>(contact.getEmails());
        Assertions.assertEquals(Utils.LIST_EMAIL_RESPONSE_DTO.size(), mails.size());
        Assertions.assertEquals(Utils.LIST_EMAIL_RESPONSE_DTO.get(0).getEmail(), mails.get(0).getEmail());
        Assertions.assertEquals(Utils.LIST_EMAIL_RESPONSE_DTO.get(0).getType().name(), mails.get(0).getType());
    }

    @Test
    void test_mapPhones_pass() {

        ContactResponseDTO payer = Utils.createContactResponseDTO();
        payer.setPhoneNumbers(Utils.createListPhoneNumberResponse());
        CustomerCommunicationSettingsDTO customerCommunicationSettingsDTO = new CustomerCommunicationSettingsDTO();
        enrichContactService.mapPhones(customerCommunicationSettingsDTO, payer);
        Assertions.assertTrue(payer.getPhoneNumbers().size() > 0);
        Assertions.assertEquals(TypePhoneNumberEnum.MOBILE, payer.getPhoneNumbers().get(0).getType());
        Assertions.assertEquals(customerCommunicationSettingsDTO.getNumber(), payer.getPhoneNumbers().get(0).getPhoneNumber());
    }

    @Test
    void test_mapPhones_number_null() {
        ContactResponseDTO payer = Utils.createContactResponseDTO();
        CustomerCommunicationSettingsDTO customerCommunicationSettingsDTO = new CustomerCommunicationSettingsDTO();
        enrichContactService.mapPhones(customerCommunicationSettingsDTO, payer);
        Assertions.assertNull(payer.getPhoneNumbers());
        Assertions.assertNull(customerCommunicationSettingsDTO.getNumber());
    }

    @Test
    void test_mapPhones_when_exit_only_fixe_then_no_map_pass() {

        ContactResponseDTO payer = Utils.createContactResponseDTO();
        payer.setPhoneNumbers(Utils.createListPhoneNumberFixe());
        CustomerCommunicationSettingsDTO customerCommunicationSettingsDTO = new CustomerCommunicationSettingsDTO();
        enrichContactService.mapPhones(customerCommunicationSettingsDTO, payer);
        Assertions.assertTrue(payer.getPhoneNumbers().size() > 0);
        Assertions.assertEquals(TypePhoneNumberEnum.FIXE, payer.getPhoneNumbers().get(0).getType());
        Assertions.assertNull(customerCommunicationSettingsDTO.getNumber());
    }

    @Test
    void testGetChannelNamebyUuid_when_ALLOW_PHONE_ENABLED_THEN_RETURN_MOBILE() {
        Mockito.when(contactClient.findPermissions(Utils.UUID)).thenReturn(Utils.createPermissionGroupListResponse());
        String channelName = enrichContactService.getChannelNameByUuid(Utils.UUID);
        Assertions.assertEquals("MOBILE", channelName);
    }

    @Test
    void testGetChannelNamebyUuid_when_ALLOW_PHONE_DISABLED_THEN_RETURN_NULL() {
        Mockito.when(contactClient.findPermissions(Utils.UUID)).thenReturn(Utils.createDisabledPermissionGroupListResponse());
        String channelName = enrichContactService.getChannelNameByUuid(Utils.UUID);
        Assertions.assertNull(channelName);
    }

    @Test
    void testGetChannelNamebyUuid_when_findPermissions_RETURN_EMPTY_Object() {
        Mockito.when(contactClient.findPermissions(Utils.UUID)).thenReturn(new PermissionGroupListResponseDTO());
        String channelName = enrichContactService.getChannelNameByUuid(Utils.UUID);
        Assertions.assertNull(channelName);
    }

    @Test
    void testGetCustomerCommunicationSettingsDTO() {
        Mockito.when(contactClient.findPermissions(Utils.UUID)).thenReturn(new PermissionGroupListResponseDTO());
        ContactResponseDTO contactResponseDTO = Utils.createContactResponseDTO();
        CommunicationPreferenceDTO communicationPreferenceDTO = Utils.createCommunicationPreferenceValue();
        contactResponseDTO.setCommunicationPreference(communicationPreferenceDTO);
        CustomerCommunicationSettingsDTO customerCommunicationSettings = enrichContactService.getCustomerCommunicationSettingsDTO(contactResponseDTO);

        Assertions.assertEquals(Utils.EMAIL, customerCommunicationSettings.getEmail());
        Assertions.assertEquals(Utils.LOCALE.toLanguageTag().toUpperCase(), customerCommunicationSettings.getLanguage());
    }

// no method named setCustomerCommunicationSetting  in the service class should this be removed
//    @Test
//    void testSetCustomerCommunicationSettings() {
//        Mockito.when(contactClient.findPermissions(Utils.UUID)).thenReturn(new PermissionGroupListResponseDTO());
//        CommunicationPreferenceDTO communicationPreferenceValue = Utils.createCommunicationPreferenceValue();
//        ContactResponseDTO owner = Utils.createContactResponseDTO();
//        CustomerDTO customer = Utils.createCustomerDTO();
//        owner.setCommunicationPreference(communicationPreferenceValue);
//
//        enrichContactService.mapCustomerCommunicationSettings(customer, owner);
//        //VERIFY
//        Assertions.assertEquals(Utils.LOCALE.toLanguageTag().toUpperCase(), customer.getCommunicationSettings().getLanguage());
//        verify(contactClient).findPermissions(Utils.UUID);
//
//    }

}

