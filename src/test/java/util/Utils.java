package util;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Locale;

import com.millicom.workflow.connector.contact.client.domain.dto.CommunicationPreferenceDTO;
import com.millicom.workflow.connector.contact.client.domain.request.ContactRequestDTO;
import com.millicom.workflow.connector.contact.client.domain.request.CreateAddressRequestDTO;
import com.millicom.workflow.connector.contact.client.domain.request.EmailRequestDTO;
import com.millicom.workflow.connector.contact.client.domain.request.IdentityDocumentRequestDTO;
import com.millicom.workflow.connector.contact.client.domain.request.PermissionGroupListRequestDTO;
import com.millicom.workflow.connector.contact.client.domain.request.PermissionGroupRequestDTO;
import com.millicom.workflow.connector.contact.client.domain.request.PermissionRequestDTO;
import com.millicom.workflow.connector.contact.client.domain.request.UpdateAddressWithTypeRequestDTO;
import com.millicom.workflow.connector.contact.client.domain.request.UpdateCommunicationPreferenceRequestDTO;
import com.millicom.workflow.connector.contact.client.domain.response.AddressResponseDTO;
import com.millicom.workflow.connector.contact.client.domain.response.ContactResponseDTO;
import com.millicom.workflow.connector.contact.client.domain.response.EmailResponseDTO;
import com.millicom.workflow.connector.contact.client.domain.response.IdentityDocumentResponseDTO;
import com.millicom.workflow.connector.contact.client.domain.response.PhoneNumberResponseDTO;
import com.millicom.workflow.connector.contact.client.domain.response.permissions.PermissionGroupListResponseDTO;
import com.millicom.workflow.connector.contact.client.domain.response.permissions.PermissionGroupResponseDTO;
import com.millicom.workflow.connector.contact.client.domain.response.permissions.PermissionResponseDTO;
import com.millicom.workflow.connector.contact.domain.dto.ContactAddressDTO;
import com.millicom.workflow.connector.contact.domain.dto.CustomerCommunicationSettingsDTO;
import com.millicom.workflow.connector.contact.domain.dto.CustomerDTO;
import com.millicom.workflow.connector.contact.domain.dto.PersonDTO;
import com.millicom.workflow.connector.contact.enums.CommunicationChannelEnum;
import com.millicom.workflow.connector.contact.enums.EmailTypeEnum;
import com.millicom.workflow.connector.contact.enums.PermissionEnum;
import com.millicom.workflow.connector.contact.enums.PermissionGroupEnum;

import com.millicom.workflow.spi.com.dto.v1.AddressDTO;
import com.millicom.workflow.spi.com.dto.v1.ContactDTO;
import com.millicom.workflow.spi.com.dto.v1.EmailDTO;
import com.millicom.workflow.spi.com.dto.v1.IdentityDocumentDTO;
import com.millicom.workflow.spi.com.dto.v1.PhoneNumberDTO;
import com.millicom.workflow.spi.com.dto.v1.enumeration.TypeEmailEnum;
import com.millicom.workflow.spi.com.dto.v1.enumeration.TypePhoneNumberEnum;
import com.millicom.workflow.spi.com.dto.v1.enumeration.AddressTypeEnum;

public class Utils {
    public static final String EMAIL = "<EMAIL>";
    public static final String NUMBER = "*********";
    public static final String FIRST_NAME = "Captain";
    public static final String LAST_NAME = "Hook";
    public static final String title = "MRS";
    static final String nationality = "COUNTRY_CY";
    static final String companyPosition = "PDG";
    static final Boolean ALLOW_THIRD_PARTY = false;
    static final LocalDate gdprValidatedAt = LocalDate.of(2020, 11, 7);
    static final String gdprLanguage = "English";
    static final EmailTypeEnum typeEmailEnum = EmailTypeEnum.MAIN;
    static final List<EmailResponseDTO> emails = createListEmailResponse();
    private static final Long ID_EMAIL = 6079L;
    private static final Long ID_PHONE_NUMBER = 3213213L;
    private static final String PHONE_NUMBER = "35796061703";
    private static final TypePhoneNumberEnum TYPE_PHONE_MOBILE = TypePhoneNumberEnum.MOBILE;
    private static final TypePhoneNumberEnum TYPE_PHONE_FIXE = TypePhoneNumberEnum.FIXE;
    private static final LocalDate BIRTH_DATE = LocalDate.of(1970, 5, 11);
    public static final String UUID = "0010213f-88aa-4f4e-a26c-0c31cd2fa20b";
    public static final Locale LOCALE = Locale.ENGLISH;
    private static LocalDate dateCreation = LocalDate.of(2020, 11, 07);
    private static LocalTime timeCreation = LocalTime.of(16, 28, 8);
    private static final LocalDateTime CREATED_AT = LocalDateTime.of(dateCreation, timeCreation);
    private static LocalDate emailDateCreation = LocalDate.of(2021, 2, 12);
    private static LocalTime emailTimeCreation = LocalTime.of(15, 14, 5);
    private static final LocalDateTime EMAIL_CREATED_AT = LocalDateTime.of(emailDateCreation, emailTimeCreation);
    static final LocalDate dateUpdate = LocalDate.of(2021, 02, 12);
    private static LocalTime timeUpdate = LocalTime.of(12, 22, 52);
    private static final LocalDateTime updatedAt = LocalDateTime.of(dateUpdate, timeUpdate);
    static final LocalDate dateExpiration = LocalDate.of(2021, 05, 8);
    public static final List<EmailResponseDTO> LIST_EMAIL_RESPONSE_DTO = createListEmailResponse();

    private static List<EmailResponseDTO> createListEmailResponse() {
        List<EmailResponseDTO> result = new ArrayList<>();
        result.add(createEmailResponse());
        return result;
    }

    public static final List<EmailRequestDTO> LIST_EMAIL_REQUEST_DTO = createListEmailRequest();

    private static List<EmailRequestDTO> createListEmailRequest() {
        List<EmailRequestDTO> result = new ArrayList<>();
        result.add(createEmailRequest());
        return result;
    }

    private static EmailResponseDTO createEmailResponse() {
        return new EmailResponseDTO(ID_EMAIL, EMAIL, typeEmailEnum, false, EMAIL_CREATED_AT, updatedAt);
    }

    private static EmailRequestDTO createEmailRequest() {
        return new EmailRequestDTO(EMAIL, typeEmailEnum.name(), false);
    }

    public static List<PhoneNumberResponseDTO> createListPhoneNumberResponse() {
        List<PhoneNumberResponseDTO> result = new ArrayList<>();
        result.add(createPhoneNumberMobile());
        result.add(createPhoneNumberFixe());
        return result;
    }

    public static List<PhoneNumberResponseDTO> createListPhoneNumberFixe() {
        List<PhoneNumberResponseDTO> result = new ArrayList<>();
        result.add(createPhoneNumberFixe());
        return result;
    }

    private static PhoneNumberResponseDTO createPhoneNumberMobile() {
        return new PhoneNumberResponseDTO(ID_PHONE_NUMBER, PHONE_NUMBER, TYPE_PHONE_MOBILE);
    }

    private static PhoneNumberResponseDTO createPhoneNumberFixe() {
        return new PhoneNumberResponseDTO(ID_PHONE_NUMBER, PHONE_NUMBER, TYPE_PHONE_FIXE);
    }

    private static List<AddressResponseDTO> createListAddressForContact() {
        List<AddressResponseDTO> result = new ArrayList<>();
        AddressResponseDTO addressResponseDTO = createAddressForContact();
        result.add(addressResponseDTO);
        return result;
    }

    private static List<CreateAddressRequestDTO> createListAddressForContactRequest() {
        List<CreateAddressRequestDTO> result = new ArrayList<>();
        CreateAddressRequestDTO createAddressRequestDTO = createAddressForContactRequest();
        result.add(createAddressRequestDTO);
        return result;
    }

    private static CreateAddressRequestDTO createAddressForContactRequest() {
        CreateAddressRequestDTO request = new CreateAddressRequestDTO();
        request.setCode("8706");
        request.setPoBox("ThePOBox");
        request.setType(AddressTypeEnum.BILLING);
        return request;
    }

    //
    // private static AddressResponseDTO createAddressResponse() {
    // AddressResponseDTO response = new AddressResponseDTO();
    // response.setAddressLine2("address 2");
    // response.setAddressLine1("address 1");
    // response.setAddressLine3("address 3");
    // response.setArea("area z");
    // response.setBuildingBlock("block A");
    // response.setBuildingType("type 1");
    // response.setCode("code 4");
    // response.setCountry("FR");
    // response.setCounty("AM");
    // response.setFlatNumber("4");
    // response.setStreet("avenue des fleurs");
    // response.setPoBox("06000");
    // response.setTown("nice");
    // response.setType(AddressTypeEnum.INSTALLATION);
    // response.setStreetNumber("6");
    // response.setStreetQualifier("");
    // return response;
    // }

    private static AddressResponseDTO createAddressForContact() {
        AddressResponseDTO response = new AddressResponseDTO();
        response.setCode("8706");
        response.setPoBox("ThePOBox");
        response.setType(AddressTypeEnum.BILLING);
        return response;
    }

    private static List<IdentityDocumentResponseDTO> createListIdentityDocumentResponse() {
        List<IdentityDocumentResponseDTO> result = new ArrayList<>();
        result.add(createIdentityDocumentResponse());
        return result;
    }

    private static IdentityDocumentResponseDTO createIdentityDocumentResponse() {
        IdentityDocumentResponseDTO response = new IdentityDocumentResponseDTO();
        response.setMainIdentityDocument(true);
        response.setIdentifier("20201107162623164");
        response.setId(5372L);
        response.setExpireAt(dateExpiration);
        response.setType("ID");
        return response;
    }

    private static List<IdentityDocumentRequestDTO> createListIdentityDocumentRequest() {
        List<IdentityDocumentRequestDTO> result = new ArrayList<>();
        result.add(createIdentityDocumentRequest());
        return result;
    }

    private static IdentityDocumentRequestDTO createIdentityDocumentRequest() {
        IdentityDocumentRequestDTO request = new IdentityDocumentRequestDTO();
        request.setMainIdentityDocument(true);
        request.setIdentifier("20201107162623164");
        request.setExpireAt(dateExpiration);
        request.setType("ID");
        return request;
    }

    public static CommunicationPreferenceDTO createCommunicationPreferenceValue() {
        CommunicationPreferenceDTO communicationPreferenceDTO = new CommunicationPreferenceDTO();
        communicationPreferenceDTO.setChannel(CommunicationChannelEnum.MOBILE_NUMBER);
        communicationPreferenceDTO.setLanguage(LOCALE);
        communicationPreferenceDTO.setEmail(EMAIL);
        communicationPreferenceDTO.setPhoneNumber("92345678");
        return communicationPreferenceDTO;
    }

    public static ContactDTO createContact(EmailDTO email, PhoneNumberDTO number, AddressDTO address,
            IdentityDocumentDTO id) {
        ContactDTO contact = new ContactDTO();
        contact.setEmails(Collections.singletonList(email));
        contact.setPhoneNumbers(Collections.singletonList(number));
        contact.setAddresses(List.of(address));
        contact.setUuid("id");
        contact.setIdentityDocuments(Collections.singletonList(id));
        return contact;
    }

    public static EmailDTO createEmailDTO() {
        return new EmailDTO(1L, EMAIL, TypeEmailEnum.MAIN, false, EMAIL_CREATED_AT, EMAIL_CREATED_AT);
    }

    public static PhoneNumberDTO createPhoneNumberDTO() {
        return new PhoneNumberDTO(1L, "123456", TYPE_PHONE_MOBILE);
    }

    public static CustomerCommunicationSettingsDTO createCustomerCommunicationSettingsDTO() {
        return new CustomerCommunicationSettingsDTO(Utils.EMAIL, "12345", "MOBILE_NUMBER", "IRL", "EN");
    }

    public static AddressDTO createContactAddressDTO() {
        return new AddressDTO(1L, AddressTypeEnum.BILLING, "addressLine1", "addressLine2", "addressLine3", "town", "county", "code", "poBox",
                "area", "country", "street", "streetNumber", "streetQualifier", "buildingType", "buildingBlock",
                "FlatNumber", "neighborhoodName", null, null);
    }

    public static ContactResponseDTO createContactResponseDTO() {
        ContactResponseDTO response = new ContactResponseDTO();
        response.setAddresses(createListAddressForContact());
        response.setCreatedAt(CREATED_AT);
        response.setCommunicationPreference(createCommunicationPreferenceValue());
        response.setEmails(LIST_EMAIL_RESPONSE_DTO);
        response.setCompanyPosition(companyPosition);
        response.setFirstName(FIRST_NAME);
        response.setGdprLanguage(gdprLanguage);
        response.setIdentityDocuments(createListIdentityDocumentResponse());
        response.setPermissions(createPermissionGroupListResponse());
        response.setLastName(LAST_NAME);
        response.setTitle(title);
        response.setUuid(UUID);
        response.setGdprValidatedAt(gdprValidatedAt);
        response.setNationality(nationality);
        response.setCreatedAt(CREATED_AT);
        return response;
    }

    public static PermissionGroupListResponseDTO createDisabledPermissionGroupListResponse() {
        PermissionGroupListResponseDTO response = new PermissionGroupListResponseDTO();
        response.setAllowThirdParty(ALLOW_THIRD_PARTY);
        List<PermissionGroupResponseDTO> permissionGroupResponseDTO = createDisabledListPermissionGroupResponse();
        response.setPermissionGroupResponseDTO(permissionGroupResponseDTO);
        return response;
    }

    private static List<PermissionGroupResponseDTO> createDisabledListPermissionGroupResponse() {
        List<PermissionGroupResponseDTO> result = new ArrayList<>();
        result.add(createDisabledPermissionGroupResponse_ACTIVE_CUSTOMER());
        return result;
    }

    private static PermissionGroupResponseDTO createDisabledPermissionGroupResponse_ACTIVE_CUSTOMER() {
        PermissionGroupResponseDTO response = new PermissionGroupResponseDTO();
        response.setPermissionGroup(PermissionGroupEnum.ACTIVE_CUSTOMER);
        response.setPermissions(createDisabledListPermissionsResponse_ACTIVE_CUSTOMER());
        response.setName("Active customer");
        return response;
    }

    private static List<PermissionResponseDTO> createDisabledListPermissionsResponse_ACTIVE_CUSTOMER() {
        List<PermissionResponseDTO> result = new ArrayList<>();
        result.add(createPermissionResponse(false, PermissionEnum.ALLOW_EMAIL_CONTACT, "EMAIL"));
        result.add(createPermissionResponse(false, PermissionEnum.ALLOW_SMS_CONTACT, "SMS"));
        result.add(createPermissionResponse(false, PermissionEnum.ALLOW_PHONE_CONTACT, "Phone"));
        result.add(createPermissionResponse(false, PermissionEnum.ALLOW_FOTS_CONTACT, "FOTS"));
        result.add(createPermissionResponse(false, PermissionEnum.ALLOW_DIRECT_MAIL_CONTACT, "Direct mail"));
        return result;
    }

    public static PermissionGroupListResponseDTO createPermissionGroupListResponse() {
        PermissionGroupListResponseDTO response = new PermissionGroupListResponseDTO();
        response.setAllowThirdParty(ALLOW_THIRD_PARTY);
        List<PermissionGroupResponseDTO> permissionGroupResponseDTO = createListPermissionGroupResponse();
        response.setPermissionGroupResponseDTO(permissionGroupResponseDTO);
        return response;
    }

    private static List<PermissionGroupResponseDTO> createListPermissionGroupResponse() {
        List<PermissionGroupResponseDTO> result = new ArrayList<>();
        result.add(createPermissionGroupResponse_ACTIVE_CUSTOMER());
        result.add(createPermissionGroupResponse_NO_LONGER_CUSTOMER());
        return result;
    }

    private static PermissionGroupResponseDTO createPermissionGroupResponse_ACTIVE_CUSTOMER() {
        PermissionGroupResponseDTO response = new PermissionGroupResponseDTO();
        response.setPermissionGroup(PermissionGroupEnum.ACTIVE_CUSTOMER);
        response.setPermissions(createListPermissionsResponse_ACTIVE_CUSTOMER());
        response.setName("Active customer");
        return response;
    }

    private static PermissionGroupResponseDTO createPermissionGroupResponse_NO_LONGER_CUSTOMER() {
        PermissionGroupResponseDTO response = new PermissionGroupResponseDTO();
        response.setPermissionGroup(PermissionGroupEnum.NO_LONGER_CUSTOMER);
        response.setPermissions(createListPermissionsResponse_NO_LONGER_CUSTOMER());
        response.setName("No longer a customer");
        return response;
    }

    private static List<PermissionResponseDTO> createListPermissionsResponse_ACTIVE_CUSTOMER() {
        List<PermissionResponseDTO> result = new ArrayList<>();
        result.add(createPermissionResponse(false, PermissionEnum.ALLOW_EMAIL_CONTACT, "EMAIL"));
        result.add(createPermissionResponse(false, PermissionEnum.ALLOW_SMS_CONTACT, "SMS"));
        result.add(createPermissionResponse(true, PermissionEnum.ALLOW_PHONE_CONTACT, "Phone"));
        result.add(createPermissionResponse(false, PermissionEnum.ALLOW_FOTS_CONTACT, "FOTS"));
        result.add(createPermissionResponse(false, PermissionEnum.ALLOW_DIRECT_MAIL_CONTACT, "Direct mail"));
        return result;
    }

    private static List<PermissionResponseDTO> createListPermissionsResponse_NO_LONGER_CUSTOMER() {
        List<PermissionResponseDTO> result = new ArrayList<>();
        result.add(createPermissionResponse(false, PermissionEnum.ALLOW_EMAIL_CONTACT, "EMAIL"));
        result.add(createPermissionResponse(false, PermissionEnum.ALLOW_SMS_CONTACT, "SMS"));
        result.add(createPermissionResponse(false, PermissionEnum.ALLOW_PHONE_CONTACT, "Phone"));
        result.add(createPermissionResponse(false, PermissionEnum.ALLOW_FOTS_CONTACT, "FOTS"));
        result.add(createPermissionResponse(false, PermissionEnum.ALLOW_DIRECT_MAIL_CONTACT, "Direct mail"));
        return result;
    }

    private static PermissionResponseDTO createPermissionResponse(Boolean enabled, PermissionEnum permissionEnum,
            String name) {
        PermissionResponseDTO response = new PermissionResponseDTO();
        response.setPermission(permissionEnum);
        response.setEnabled(enabled);
        response.setName(name);
        return response;
    }

    private static PermissionGroupListRequestDTO createPermissionGroupListRequest() {
        PermissionGroupListRequestDTO request = new PermissionGroupListRequestDTO();
        List<PermissionGroupRequestDTO> permissionGroupRequestDTO = createListPermissionGroupRequest();
        request.setPermissionGroups(permissionGroupRequestDTO);
        return request;
    }

    private static List<PermissionGroupRequestDTO> createListPermissionGroupRequest() {
        List<PermissionGroupRequestDTO> result = new ArrayList<>();
        result.add(createPermissionGroupRequest_ACTIVE_CUSTOMER());
        result.add(createPermissionGroupRequest_NO_LONGER_CUSTOMER());
        return result;
    }

    private static PermissionGroupRequestDTO createPermissionGroupRequest_ACTIVE_CUSTOMER() {
        PermissionGroupRequestDTO request = new PermissionGroupRequestDTO();
        request.setPermissionGroup(PermissionGroupEnum.ACTIVE_CUSTOMER);
        request.setPermissions(createListPermissionsRequest_ACTIVE_CUSTOMER());
        return request;
    }

    private static PermissionGroupRequestDTO createPermissionGroupRequest_NO_LONGER_CUSTOMER() {
        PermissionGroupRequestDTO request = new PermissionGroupRequestDTO();
        request.setPermissionGroup(PermissionGroupEnum.NO_LONGER_CUSTOMER);
        request.setPermissions(createListPermissionsRequest_NO_LONGER_CUSTOMER());
        return request;
    }

    private static List<PermissionRequestDTO> createListPermissionsRequest_ACTIVE_CUSTOMER() {
        List<PermissionRequestDTO> result = new ArrayList<>();
        result.add(createPermissionRequest(false, PermissionEnum.ALLOW_EMAIL_CONTACT, "EMAIL"));
        result.add(createPermissionRequest(false, PermissionEnum.ALLOW_SMS_CONTACT, "SMS"));
        result.add(createPermissionRequest(true, PermissionEnum.ALLOW_PHONE_CONTACT, "Phone"));
        result.add(createPermissionRequest(false, PermissionEnum.ALLOW_FOTS_CONTACT, "FOTS"));
        result.add(createPermissionRequest(false, PermissionEnum.ALLOW_DIRECT_MAIL_CONTACT, "Direct mail"));
        return result;
    }

    private static List<PermissionRequestDTO> createListPermissionsRequest_NO_LONGER_CUSTOMER() {
        List<PermissionRequestDTO> result = new ArrayList<>();
        result.add(createPermissionRequest(false, PermissionEnum.ALLOW_EMAIL_CONTACT, "EMAIL"));
        result.add(createPermissionRequest(false, PermissionEnum.ALLOW_SMS_CONTACT, "SMS"));
        result.add(createPermissionRequest(false, PermissionEnum.ALLOW_PHONE_CONTACT, "Phone"));
        result.add(createPermissionRequest(false, PermissionEnum.ALLOW_FOTS_CONTACT, "FOTS"));
        result.add(createPermissionRequest(false, PermissionEnum.ALLOW_DIRECT_MAIL_CONTACT, "Direct mail"));
        return result;
    }

    private static PermissionRequestDTO createPermissionRequest(Boolean enabled, PermissionEnum permissionEnum,
            String name) {
        PermissionRequestDTO request = new PermissionRequestDTO();
        request.setPermission(permissionEnum);
        request.setEnabled(enabled);
        return request;
    }

    public static ContactRequestDTO createContactRequest() {
        ContactRequestDTO request = new ContactRequestDTO();
        request.setAddresses(createListAddressForContactRequest());
        request.setCommunicationPreference(createCommunicationPreferenceValue());
        List<EmailRequestDTO> emails = new ArrayList<>();
        request.setEmails(emails);
        request.setCompanyPosition(companyPosition);
        request.setFirstName(FIRST_NAME);
        request.setGdprLanguage(Locale.ENGLISH);
        request.setGdprValidationDate(updatedAt);
        request.setPermissionGroupList(createPermissionGroupListRequest());
        request.setIdentityDocuments(createListIdentityDocumentRequest());
        request.setPermissionGroupList(createPermissionGroupListRequest());
        request.setLastName(LAST_NAME);
        request.setTitle(title);
        request.setNationality(nationality);
        request.setPhoneNumbers(new ArrayList<>());
        return request;
    }

    private static EmailRequestDTO createListContactEmailDTO() {
        EmailRequestDTO emailRequestDTO = new EmailRequestDTO();
        emailRequestDTO.setEmail(EMAIL);
        emailRequestDTO.setType(EmailTypeEnum.MAIN.name());
        emailRequestDTO.setValidated(false);
        return emailRequestDTO;
    }

    public static UpdateCommunicationPreferenceRequestDTO updateCommunicationPreferenceRequestDTO() {
        UpdateCommunicationPreferenceRequestDTO updateCommunicationPreferenceRequestDTO = new UpdateCommunicationPreferenceRequestDTO();
        updateCommunicationPreferenceRequestDTO.setChannel(CommunicationChannelEnum.EMAIL);
        updateCommunicationPreferenceRequestDTO.setEmail(EMAIL);
        updateCommunicationPreferenceRequestDTO.setLanguage(Locale.ENGLISH);
        updateCommunicationPreferenceRequestDTO.setPhoneNumber(PHONE_NUMBER);
        return updateCommunicationPreferenceRequestDTO;
    }

    public static UpdateAddressWithTypeRequestDTO updateAddressRequest() {
        UpdateAddressWithTypeRequestDTO request = new UpdateAddressWithTypeRequestDTO();
        request.setCode("8706");
        request.setPoBox("ThePOBox");
        request.setType(AddressTypeEnum.BILLING);
        return request;
    }

    public static PermissionGroupListRequestDTO updatePermissionRequestDTO() {
        PermissionGroupListRequestDTO request = new PermissionGroupListRequestDTO();
        List<PermissionGroupRequestDTO> permissionGroupRequestDTO = createListPermissionGroupRequest();
        request.setAllowThirdParty(ALLOW_THIRD_PARTY);
        request.setPermissionGroups(permissionGroupRequestDTO);
        return request;
    }

    public static CustomerDTO createCustomerDTO() {
        CustomerDTO customerDTO = new CustomerDTO();
        customerDTO.setCommunicationSettings(Utils.createCustomerCommunicationSettingsDTO());
        return customerDTO;
    }
}
