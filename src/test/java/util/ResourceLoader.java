package util;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import org.apache.commons.io.IOUtils;

import java.io.IOException;

import static java.util.Objects.isNull;

public class ResourceLoader {

    private final ObjectMapper objectMapper;

    public ResourceLoader() {
        objectMapper = new ObjectMapper();
        objectMapper.registerModule(new JavaTimeModule());
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    }

    public <T> T loadResource(String path, Class<T> valueType) throws IOException {
        var inputStream = getClass().getClassLoader().getResourceAsStream(path);
        if (isNull(inputStream)) throw new IOException("Input stream is null");
        String requestJson = IOUtils.toString(inputStream);
        return objectMapper.readValue(requestJson, valueType);
    }

    public ObjectMapper getObjectMapper() {
        return objectMapper;
    }
}
