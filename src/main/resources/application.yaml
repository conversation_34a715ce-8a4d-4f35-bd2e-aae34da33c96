server:
  port: 6080

application:
  name: @project.name@
  version: @project.version@

logging:
  level:
    mc:
      monacotelecom: DEBUG
    org:
      springframework: INFO
    root: INFO
    com:
      millicom:
        workflow:
          connector:
            contact:
              client: DEBUG
feign:
  client:
    config:
      default:
        loggerLevel: full

spring:
  application:
    name: @project.name@
  profiles:
    active: "eir"
  main:
    allow-bean-definition-overriding: true
  cloud:
    config:
      discovery:
        enabled: false
      enabled: false
  zipkin:
    enabled: false
  rabbitmq:
    host: localhost
    port: 5672
    username:
    password:

maven:
  project:
    artifactId: contact-workflow-connector
    version: ${project.version}

webservice:
  contact:
    url:
  account:
    url:
