server:
  port: 6081

application:
  name: @project.name@
  version: @project.version@

logging:
  level:
    mc:
      monacotelecom: DEBUG
    org:
      springframework: INFO
    root: INFO
    com:
      millicom:
        workflow:
          connector:
            contact:
              client: DEBUG
              mappers:
                AddressMapper: DEBUG

feign:
  client:
    config:
      default:
        loggerLevel: full

spring:
  application:
    name: @project.name@
  profiles:
    active: "dev"
  main:
    allow-bean-definition-overriding: true
  cloud:
    config:
      discovery:
        enabled: false
      enabled: false
  zipkin:
    enabled: false
  rabbitmq:
    host: localhost
    port: 5672
    username: guest
    password: guest

maven:
  project:
    artifactId: contact-workflow-connector
    version: ${project.version}

webservice:
  contact:
    url: https://glx-crm-pa-stg.tigo.cam/contact-management
  account:
    url: https://glx-crm-pa-stg.tigo.cam/subscription-management