spring:
  cloud:
    stream:
      default:
        contentType: application/json
        group: wkf-contact-workflow-connector
        consumer:
          max-attempts: 1
      bindings:
        wkf_contact_create_contact:
          destination: wkf_contact_create_contact
        wkf_contact_getcontact:
          destination: wkf_contact_getcontact
        wkf_contact_create_address:
          destination: wkf_contact_create_address
        wkf_contact_create_contacts:
          destination: wkf_contact_create_contacts
        wkf_contact_validate_email:
          destination: wkf_contact_validate_email
        wkf_contact_enrich_contact:
          destination: wkf_contact_enrich_contact
        wkf_contact_update_installation_address:
          destination: wkf_contact_update_installation_address
        wkf_contact_update_communication_settings:
          destination: wkf_contact_update_communication_settings
        wkf_contact_forget_Email:
          destination: wkf_contact_forget_Email
        wkf_contact_forget_address:
          destination: wkf_contact_forget_address
        wkf_contact_forget_contacts:
          destination: wkf_contact_forget_contacts
        wkf_contact_forget_comm_settings:
          destination: wkf_contact_forget_comm_settings
        wkf_contact_anonymize:
          destination: wkf_contact_anonymize
        wkf_contact_getaccountcontacts:
          destination: wkf_contact_getaccountcontacts