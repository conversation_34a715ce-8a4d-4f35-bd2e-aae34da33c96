package com.millicom.workflow.connector.contact.enums;

import java.util.stream.Stream;

public enum PermissionGroupEnum {
    ACTIVE_CUSTOMER("Active customer"),
    NO_LONGER_CUSTOMER("No longer a customer");

    private String value;

    private PermissionGroupEnum(String value) {
        this.value = value;
    }

    public static Stream<PermissionGroupEnum> stream() {
        return Stream.of(values());
    }

    public String getValue() {
        return this.value;
    }
}
