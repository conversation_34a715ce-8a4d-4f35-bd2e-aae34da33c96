package com.millicom.workflow.connector.contact;

import mc.monacotelecom.common.core.lib.util.YamlPropertySourceFactory;
import mc.monacotelecom.workflow.connector.annotation.EnableWorkflowConnectorHistory;
import org.activiti.cloud.connectors.starter.configuration.EnableActivitiCloudConnector;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.cloud.stream.annotation.EnableBinding;
import org.springframework.context.annotation.PropertySource;

import com.millicom.workflow.connector.contact.stream.channel.ContactConnectorChannels;

@SpringBootApplication(scanBasePackages = {"com.millicom.workflow.connector.contact","mc.monacotelecom.workflow","org.activiti.cloud.connectors.starter"})
@EnableActivitiCloudConnector
@EnableWorkflowConnectorHistory
@EnableFeignClients
@EnableBinding({ContactConnectorChannels.class})
@PropertySource(value = "classpath:application-spring-cloud-stream-bindings.yaml", factory = YamlPropertySourceFactory.class)
@PropertySource(value = "classpath:application-spring-cloud-stream-rabbitmq-bindings.yaml", factory = YamlPropertySourceFactory.class)
public class ContactConnectorApplication {
    public static void main(String[] args) {
        SpringApplication.run(ContactConnectorApplication.class, args);
    }
}
