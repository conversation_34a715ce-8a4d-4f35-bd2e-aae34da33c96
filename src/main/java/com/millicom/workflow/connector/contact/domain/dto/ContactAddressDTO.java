package com.millicom.workflow.connector.contact.domain.dto;

import com.millicom.workflow.spi.com.dto.v1.enumeration.AddressTypeEnum;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class ContactAddressDTO {

    private Long id;
    private String city;
    private String name;
    private String buildingName;
    private String addressLine1;
    private String addressLine2;
    private String addressLine3;
    private String county;
    private AddressTypeEnum type;
    private String code;
    private String postalCode;
    private String country;
    private String poBox;
    private String area;
    private String streetQualifier;
    private String streetNumber;
    private String buildingType;
    private String flatNumber;

}
