package com.millicom.workflow.connector.contact.domain.request;


import com.millicom.workflow.connector.contact.domain.dto.CartDTO;
import com.millicom.workflow.connector.contact.domain.dto.ContactDTO;
import com.millicom.workflow.connector.contact.domain.dto.InvoiceSettingsDTO;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
public class ValidateEmailRequest {

    private String correlationId;
    private String customerReference;
    private InvoiceSettingsDTO invoiceSettings;
    private ContactDTO payer;
    private ContactDTO owner;
    private CartDTO cart;
}
