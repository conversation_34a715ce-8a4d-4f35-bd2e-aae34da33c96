package com.millicom.workflow.connector.contact.client.configuration;

import feign.RequestInterceptor;
import feign.RequestTemplate;
import org.springframework.context.annotation.Configuration;

@Configuration
public class GalaxionHeadersConfiguration implements RequestInterceptor {

    @Override
    public void apply(RequestTemplate requestTemplate) {
        requestTemplate
                .header("galaxion-user-identifier", "SYSTEM")
                .header("galaxion-user-type", "SYSTEM");
    }
}
