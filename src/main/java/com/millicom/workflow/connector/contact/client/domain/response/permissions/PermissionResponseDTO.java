package com.millicom.workflow.connector.contact.client.domain.response.permissions;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.millicom.workflow.connector.contact.enums.PermissionEnum;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class PermissionResponseDTO {

    private PermissionEnum permission;

    private String name;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Boolean enabled;
}
