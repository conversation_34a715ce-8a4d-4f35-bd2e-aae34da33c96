package com.millicom.workflow.connector.contact.domain.request;

import com.millicom.workflow.connector.contact.client.domain.request.ContactRequestDTO;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
public class CreateContactRequest {
    private String correlationId;
    private String customerReference;
    private ContactRequestDTO contact;
}
