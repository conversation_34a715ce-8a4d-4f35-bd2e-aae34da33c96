package com.millicom.workflow.connector.contact.stream.listener;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import mc.monacotelecom.workflow.connector.common.service.CommonIntegrationService;
import org.activiti.cloud.api.process.model.IntegrationRequest;
import org.springframework.cloud.stream.annotation.StreamListener;
import org.springframework.stereotype.Component;

import com.millicom.workflow.connector.contact.domain.request.EnrichContactRequest;
import com.millicom.workflow.connector.contact.service.EnrichContactService;

import static com.millicom.workflow.connector.contact.stream.channel.ContactConnectorChannels.WKF_CONTACT_ENRICH_CONTACT;

import java.util.Map;

@Slf4j
@Component
@RequiredArgsConstructor
//TODO
/**
 * This service should be removed. ContactRetrieveService.getContact should be used instead and in BPMN we should have 2 tasks, one for owner and one for payer.
 * Mapping should be done in JSON files in OPCO configuration project
 */
public class EnrichContactsListener {

    private final EnrichContactService enrichContactService;
    private final CommonIntegrationService commonIntegrationService;

    @StreamListener(WKF_CONTACT_ENRICH_CONTACT)
    public void enrichContact(IntegrationRequest event) {
        var request = commonIntegrationService.getListenerInput(event, EnrichContactRequest.class);
        
        var contact = enrichContactService.callEnrichContact(request);
        Map<String, Object> outBoundVariable = Map.of("contact", contact);

        commonIntegrationService.sendResult(event, outBoundVariable);
    }
}
