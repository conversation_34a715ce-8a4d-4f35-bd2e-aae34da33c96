package com.millicom.workflow.connector.contact.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.millicom.workflow.connector.contact.client.ContactClient;
import com.millicom.workflow.connector.contact.client.domain.response.AddressResponseDTO;
import com.millicom.workflow.connector.contact.client.domain.response.ContactResponseDTO;
import com.millicom.workflow.connector.contact.client.domain.response.IdentityDocumentResponseDTO;
import com.millicom.workflow.connector.contact.client.domain.response.PhoneNumberResponseDTO;
import com.millicom.workflow.connector.contact.client.domain.response.permissions.PermissionGroupResponseDTO;
import com.millicom.workflow.connector.contact.client.domain.response.permissions.PermissionResponseDTO;
import com.millicom.workflow.connector.contact.domain.dto.ContactAddressDTO;
import com.millicom.workflow.connector.contact.domain.dto.ContactDTO;
import com.millicom.workflow.connector.contact.domain.dto.CustomerCommunicationSettingsDTO;
import com.millicom.workflow.connector.contact.domain.dto.CustomerIdentityDocumentDTO;
import com.millicom.workflow.connector.contact.domain.dto.EmailDTO;
import com.millicom.workflow.connector.contact.domain.dto.PersonDTO;
import com.millicom.workflow.connector.contact.domain.dto.PhoneNumberDTO;
import com.millicom.workflow.connector.contact.domain.request.EnrichContactRequest;
import com.millicom.workflow.connector.contact.domain.response.EnrichContactResponse;
import com.millicom.workflow.connector.contact.enums.EmailTypeEnum;
import com.millicom.workflow.connector.contact.enums.PermissionEnum;
import com.millicom.workflow.connector.contact.enums.PermissionGroupEnum;
import com.millicom.workflow.connector.contact.enums.TypePhoneNumberEnum;
import com.millicom.workflow.connector.contact.exception.ContactConnectorException;

import static com.millicom.workflow.connector.contact.mappers.EnrichContactMapper.toEnrichContactResponse;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.function.Predicate;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class EnrichContactService {

    private final ContactClient contactClient;

    public EnrichContactResponse callEnrichContact(EnrichContactRequest enrichContactRequest) {
        mapOwner(enrichContactRequest);
        mapPayer(enrichContactRequest);

        return toEnrichContactResponse(enrichContactRequest);
    }

    public void mapCustomerCommunicationSettings(EnrichContactRequest enrichContactRequest, ContactResponseDTO owner) {
        if (enrichContactRequest.getCommunicationSettings() == null && owner.getCommunicationPreference() != null) {
            var customerCommunicationSetting = getCustomerCommunicationSettingsDTO(owner);
            enrichContactRequest.setCommunicationSettings(customerCommunicationSetting);
        }
    }

    public CustomerCommunicationSettingsDTO getCustomerCommunicationSettingsDTO(ContactResponseDTO contactResponseDTO) {

        var communicationSettings = new CustomerCommunicationSettingsDTO();
        var communicationPreference = contactResponseDTO.getCommunicationPreference();
        communicationSettings.setLanguage(communicationPreference.getLanguage() != null ? communicationPreference.getLanguage().toLanguageTag().toUpperCase() : null);
        //BBHB case is not preview to have the communicationPreferences
        String channelName = getChannelNameByUuid(contactResponseDTO.getUuid());
        communicationSettings.setPreferredChannel(channelName);
        communicationSettings.setEmail(communicationPreference.getEmail());
        mapPhones(communicationSettings, contactResponseDTO);
        return communicationSettings;
    }

    public String getChannelNameByUuid(String uuid) {
        var permissionGroupListResponse = contactClient.findPermissions(uuid);
        List<PermissionGroupResponseDTO> activeCustomerPermissionGroup = permissionGroupListResponse.getPermissionGroupResponseDTO().stream().filter(permissionGroupDTO -> PermissionGroupEnum.ACTIVE_CUSTOMER.equals(permissionGroupDTO.getPermissionGroup())).collect(Collectors.toList());
        List<PermissionResponseDTO> permissions = activeCustomerPermissionGroup.stream().findFirst().orElseGet(PermissionGroupResponseDTO::new).getPermissions();
        List<PermissionResponseDTO> enabledPermissions = permissions.stream().filter(permissionsEnabled()).collect(Collectors.toList());

        List<String> eligiblePermissions = enabledPermissions.stream()
                                                             .filter(permissionsEligibles())
                                                             .map(PermissionResponseDTO::getName)
                                                             .collect(Collectors.toList());

        if (eligiblePermissions.contains(PermissionEnum.ALLOW_PHONE_CONTACT.getValue())) {
            return "MOBILE";
        } else if (eligiblePermissions.contains(PermissionEnum.ALLOW_EMAIL_CONTACT.getValue())) {
            return "EMAIL";
        } else if (eligiblePermissions.contains(PermissionEnum.ALLOW_SMS_CONTACT.getValue())) {
            return "SMS";
        }
        return null;
    }

    public void mapEmails(ContactDTO contact, ContactResponseDTO payer) {
        contact.setEmails(Optional.ofNullable(payer.getEmails())
                                  .orElseGet(ArrayList::new)
                                  .stream()
                                  .filter(emailValue -> EmailTypeEnum.MAIN.name().equals(emailValue.getType().name()))
                                  .map(emailValue -> {
                                      EmailDTO emailDTO = new EmailDTO();
                                      emailDTO.setId(emailValue.getId());
                                      emailDTO.setEmail(emailValue.getEmail());
                                      emailDTO.setType(EmailTypeEnum.MAIN.name());
                                      return emailDTO;
                                  })
                                  .collect(Collectors.toSet()));
    }

    public void mapPhones(CustomerCommunicationSettingsDTO customerCommunicationSetting, ContactResponseDTO payer) {
        customerCommunicationSetting.setNumber(Optional.ofNullable(payer.getPhoneNumbers())
                                                       .orElseGet(ArrayList::new)
                                                       .stream()
                                                       .filter(phoneNumberValue -> TypePhoneNumberEnum.MOBILE.name().equals(phoneNumberValue.getType().name()))
                                                       .findFirst().orElseGet(PhoneNumberResponseDTO::new).getPhoneNumber());
    }

    private void mapPerson(ContactDTO contactDTO) {
        if (contactDTO != null && contactDTO.getPerson() == null) {
            ContactResponseDTO userLine = contactClient.getContact(contactDTO.getContactUuid());

            PersonDTO person = new PersonDTO(
                    userLine.getFirstName(),
                    userLine.getLastName(),
                    userLine.getTitle() != null ? userLine.getTitle() : null,
                    userLine.getBirthDate(),
                    userLine.getNationality());
            contactDTO.setPerson(person);

            mapCompanyPosition(contactDTO, userLine);
            mapNumbers(contactDTO, userLine);
            mapEmails(contactDTO, userLine);
            mapAddress(contactDTO, userLine);
        }
    }

    private void mapCompanyPosition(ContactDTO contact, ContactResponseDTO payer) {
        contact.setCompanyPosition(payer.getCompanyPosition());
    }

    void mapNumbers(ContactDTO contact, ContactResponseDTO payer) {

        if (payer.getPhoneNumbers() != null) {
            Set<PhoneNumberDTO> numbers = new HashSet<>();

            for (PhoneNumberResponseDTO phoneValue : payer.getPhoneNumbers()) {
                PhoneNumberDTO phoneNumberDTO = new PhoneNumberDTO();
                phoneNumberDTO.setNumber(phoneValue.getPhoneNumber());
                phoneNumberDTO.setType(phoneValue.getType().name());
                numbers.add(phoneNumberDTO);
            }

            contact.setNumbers(numbers);
        }
    }

    void mapAddress(ContactDTO contact, ContactResponseDTO contactResponse) {

        if (!CollectionUtils.isEmpty(contactResponse.getAddresses())) {
            ContactAddressDTO addressDTO = new ContactAddressDTO();

            AddressResponseDTO address = contactResponse.getAddresses().stream().findFirst().orElse(null);
            if (address != null) {
                addressDTO.setPostalCode(address.getCode());
                addressDTO.setCity(address.getTown());
                addressDTO.setArea(address.getArea());
                if (address.getPoBox() != null) addressDTO.setPoBox(address.getPoBox());
                addressDTO.setStreetNumber(address.getStreetNumber());
                addressDTO.setName(address.getStreet());
                addressDTO.setBuildingType(address.getBuildingType());
                addressDTO.setBuildingName(address.getBuildingBlock());
                addressDTO.setFlatNumber(address.getFlatNumber());
            }
            contact.setAddresses(List.of(addressDTO));
        }
    }

    private void mapOwner(EnrichContactRequest enrichContactRequest) {

        if (enrichContactRequest != null && enrichContactRequest.getOwner() != null && enrichContactRequest.getOwner().getPerson() == null) {

            String ownerUUID = Optional.of(enrichContactRequest.getOwner().getContactUuid()).orElseThrow(() -> new ContactConnectorException("Owner UUID must not be null"));

            ContactResponseDTO owner = contactClient.getContact(ownerUUID);
            ContactDTO ownerContact = new ContactDTO();
            ownerContact.setContactUuid(ownerUUID);
            enrichContactRequest.setOwner(ownerContact);

            PersonDTO person = new PersonDTO(
                    owner.getFirstName(),
                    owner.getLastName(),
                    owner.getTitle() != null ? owner.getTitle() : null,
                    owner.getBirthDate(),
                    owner.getNationality());
            enrichContactRequest.getOwner().setPerson(person);

            mapCustomerCommunicationSettings(enrichContactRequest, owner);
            mapEmails(enrichContactRequest.getOwner(), owner);
            mapCustomerCommunicationSettings(enrichContactRequest, owner);
            mapCompanyPosition(enrichContactRequest.getOwner(), owner);
            mapNumbers(enrichContactRequest.getOwner(), owner);
            mapEmails(enrichContactRequest.getOwner(), owner);
            mapIdentityDocuments(enrichContactRequest.getOwner(), owner);
            mapAddress(enrichContactRequest.getOwner(), owner);
        }
    }

    void mapIdentityDocuments(ContactDTO contact, ContactResponseDTO owner) {

        if (owner.getIdentityDocuments() != null) {
            Set<CustomerIdentityDocumentDTO> identityDocuments = new HashSet<>();

            for (IdentityDocumentResponseDTO identityDocument : owner.getIdentityDocuments()) {
                CustomerIdentityDocumentDTO identityDocumentDTO = new CustomerIdentityDocumentDTO();

                identityDocumentDTO.setExpirationDate(identityDocument.getExpireAt());
                identityDocumentDTO.setIdentifier(identityDocument.getIdentifier());
                identityDocumentDTO.setType(identityDocument.getType());
                identityDocument.setMainIdentityDocument(identityDocument.getMainIdentityDocument());

                identityDocuments.add(identityDocumentDTO);
            }

            contact.setIdentityDocuments(identityDocuments);
        }
    }

    private Predicate<PermissionResponseDTO> permissionsEligibles() {
        return permissionResponseDTO -> PermissionEnum.ALLOW_SMS_CONTACT.getValue().equals(permissionResponseDTO.getName()) || PermissionEnum.ALLOW_PHONE_CONTACT.getValue().equals(permissionResponseDTO.getName()) || PermissionEnum.ALLOW_EMAIL_CONTACT.getValue().equals(permissionResponseDTO.getName());
    }

    private Predicate<PermissionResponseDTO> permissionsEnabled() {
        return permission -> Boolean.TRUE.equals(permission.getEnabled());
    }

    private void mapPayer(EnrichContactRequest enrichContactRequest) {
        if (enrichContactRequest != null) {
            ContactDTO payerContact = enrichContactRequest.getPayer();
            mapPerson(payerContact);
        }
    }

}
