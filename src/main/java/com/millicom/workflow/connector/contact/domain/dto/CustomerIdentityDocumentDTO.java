package com.millicom.workflow.connector.contact.domain.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.LocalDate;

@Data
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class CustomerIdentityDocumentDTO {


    private String type;
    private String identifier;
    private LocalDate expirationDate;
    private String nationality;
    private boolean mainIdentityDocument;
}
