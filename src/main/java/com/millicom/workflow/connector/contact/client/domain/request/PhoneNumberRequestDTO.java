package com.millicom.workflow.connector.contact.client.domain.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import com.millicom.workflow.spi.com.dto.v1.enumeration.TypePhoneNumberEnum;


@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class PhoneNumberRequestDTO {

    @NotBlank
    private String phoneNumber;

    @NotNull
    private TypePhoneNumberEnum type;
}
