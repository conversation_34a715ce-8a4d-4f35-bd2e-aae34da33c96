package com.millicom.workflow.connector.contact.client.domain.response;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class IdentityDocumentResponseDTO {

    private Long id;
    private Boolean mainIdentityDocument;
    private String identifier;
    private String type;
    private String nationality;
    private LocalDate expireAt;
}
