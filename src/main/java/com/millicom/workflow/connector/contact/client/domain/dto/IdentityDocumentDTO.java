package com.millicom.workflow.connector.contact.client.domain.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class IdentityDocumentDTO {
    @ApiModelProperty(value = "The Identifier document is the main or not", example = "true", required = true)
    private Boolean mainIdentityDocument;

    @ApiModelProperty(value = "Identifier document", example = "abc123", required = true)
    private String identifier;

    @ApiModelProperty(value = "Document type", example = "abc123", required = true)
    private String type;

    @ApiModelProperty(value = "Nationality of the customer", example = "Français")
    private String nationality;

    @ApiModelProperty(value = "Expiration date.", example = "1951-05-04")
    private LocalDate expirationDate;
}
