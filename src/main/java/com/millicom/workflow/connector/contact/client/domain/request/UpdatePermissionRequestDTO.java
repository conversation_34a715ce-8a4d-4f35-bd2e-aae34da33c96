package com.millicom.workflow.connector.contact.client.domain.request;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

import com.millicom.workflow.connector.contact.client.domain.dto.PermissionDTO;
import com.millicom.workflow.connector.contact.enums.PermissionGroupEnum;

import java.util.SortedSet;
import java.util.TreeSet;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class UpdatePermissionRequestDTO {
    @NotNull
    private PermissionGroupEnum permissionGroup;
    @NotNull
    private SortedSet<PermissionDTO> permissions = new TreeSet<>();
}
