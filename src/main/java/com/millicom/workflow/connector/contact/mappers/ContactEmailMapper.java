package com.millicom.workflow.connector.contact.mappers;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.stream.Collectors;

import com.millicom.workflow.connector.contact.client.domain.request.EmailRequestDTO;
import com.millicom.workflow.spi.com.dto.v1.EmailDTO;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class ContactEmailMapper {

        public static List<EmailRequestDTO> toEmailRequestList(List<EmailDTO> customerEmails) {
            return customerEmails.stream()
                    .map(ContactEmailMapper::toEmailRequest)
                    .collect(Collectors.toList());
        }

        private static EmailRequestDTO toEmailRequest(EmailDTO customerEmailDTO) {
            return EmailRequestDTO.builder()
                    .email(customerEmailDTO.getEmail())
                    .type(customerEmailDTO.getType().name())
                    .validated(Boolean.FALSE)
                    .build();
        }
}
