package com.millicom.workflow.connector.contact.domain.response;

import java.util.List;

import com.millicom.workflow.connector.contact.client.domain.response.ContactResponseDTO;
import com.millicom.workflow.connector.contact.domain.dto.CustomerCommunicationSettingsDTO;
import com.millicom.workflow.spi.com.dto.v1.ContactDTO;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Setter
@Getter
@NoArgsConstructor
@ToString
public class CustomerContactsResponse {

    private ContactResponseDTO owner;
    private ContactResponseDTO payer;
    private CustomerCommunicationSettingsDTO communicationSettings;
    private List<ContactDTO> contacts;
}
