package com.millicom.workflow.connector.contact.stream.channel;

import org.springframework.cloud.stream.annotation.Input;
import org.springframework.messaging.SubscribableChannel;

public interface ContactConnectorChannels {
    String WKF_CONTACT_VALIDATE_EMAIL = "wkf_contact_validate_email";
    String WKF_CONTACT_ENRICH_CONTACT = "wkf_contact_enrich_contact";
    String WKF_CONTACT_UPDATE_INSTALLATION_ADDRESS = "wkf_contact_update_installation_address";
    String WKF_CONTACT_UPDATE_COMMUNICATION_SETTINGS = "wkf_contact_update_communication_settings";
    String WKF_CONTACT_CREATE_CONTACTS = "wkf_contact_create_contacts" ;
    String WKF_CONTACT_CREATE_CONTACT = "wkf_contact_create_contact";
    String WKF_CONTACT_CREATE_EMAIL_CONTACTS = "wkf_contact_create_email_contacts";
    String WKF_CONTACT_UPDATE_MARKETING_PREFS = "wkf_contact_update_marketing_prefs";
    String WKF_CONTACT_FORGET_CONTACTS = "wkf_contact_forget_contacts";
    String WKF_CONTACT_FORGET_EMAIL = "wkf_contact_forget_Email";
    String WKF_CONTACT_FORGET_ADDRESS = "wkf_contact_forget_address";
    String WKF_CONTACT_FORGET_COMM_SETTINGS = "wkf_contact_forget_comm_settings";
    String WKF_CONTACT_ANONYMIZE = "wkf_contact_anonymize";
    String WKF_CONTACT_GETCONTACT = "wkf_contact_getcontact";
    String WKF_CONTACT_GETACCOUNTCONTACTS = "wkf_contact_getaccountcontacts";

    @Input(WKF_CONTACT_ANONYMIZE)
    SubscribableChannel anonymizeContact();

    @Input(WKF_CONTACT_CREATE_CONTACTS)
    SubscribableChannel createContacts();

    @Input(WKF_CONTACT_CREATE_CONTACT)
    SubscribableChannel createContact();

    @Input(WKF_CONTACT_VALIDATE_EMAIL)
    SubscribableChannel validateEmail();

    @Input(WKF_CONTACT_ENRICH_CONTACT)
    SubscribableChannel enrichContact();

    @Input(WKF_CONTACT_UPDATE_INSTALLATION_ADDRESS)
    SubscribableChannel updateInstallationAddress();

    @Input(WKF_CONTACT_UPDATE_COMMUNICATION_SETTINGS)
    SubscribableChannel updateCommunicationSettings();

    @Input(WKF_CONTACT_UPDATE_MARKETING_PREFS)
    SubscribableChannel updateMarketingPrefs();

    @Input(WKF_CONTACT_FORGET_EMAIL)
    SubscribableChannel forgetEmail();

    @Input(WKF_CONTACT_FORGET_ADDRESS)
    SubscribableChannel forgetAddress();

    @Input(WKF_CONTACT_FORGET_CONTACTS)
    SubscribableChannel forgetContacts();

    @Input(WKF_CONTACT_FORGET_COMM_SETTINGS)
    SubscribableChannel forgetCommunicationSettings();

    @Input(WKF_CONTACT_GETCONTACT)
    SubscribableChannel getContact();

    @Input(WKF_CONTACT_CREATE_EMAIL_CONTACTS)
    SubscribableChannel createContactEmail();

    @Input(WKF_CONTACT_GETACCOUNTCONTACTS)
    SubscribableChannel getAccountContacts();

}
