
package com.millicom.workflow.connector.contact.mappers;
import com.millicom.workflow.connector.contact.client.domain.request.CreateAddressRequestDTO;
import com.millicom.workflow.spi.com.dto.v1.AddressDTO;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import java.util.List;
import java.util.Arrays;
import java.util.stream.Collectors;
import com.millicom.workflow.spi.com.dto.v1.enumeration.AddressTypeEnum;

@Slf4j
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class AddressMapper {

    public static CreateAddressRequestDTO toCreateRequest(AddressDTO address) {
        if (address == null) {
            log.warn("AddressDTO is null. Returning empty CreateAddressRequestDTO.");
            return new CreateAddressRequestDTO();
        }
        
        CreateAddressRequestDTO dto = CreateAddressRequestDTO.builder()
                .type(address.getType())
                .addressLine1(address.getAddressLine1())
                .addressLine2(address.getAddressLine2())
                .addressLine3(address.getAddressLine3())
                .town(address.getTown())                
                .code(address.getCode())
                .area(address.getArea())
                .country(address.getCountry())
                .street(buildStreet(address)) //cambio requerido
                .streetNumber(address.getStreetNumber())
                .streetQualifier(address.getStreetQualifier())
                .buildingBlock(address.getBuildingBlock())
                .poBox(address.getPoBox())
                .buildingType(address.getBuildingType())
                .build();

        log.debug("Address mapeada: {}", dto);
        return dto;
    }

 private static String buildStreet(AddressDTO address) {
    if (AddressTypeEnum.BILLING.equals(address.getType())){
        List<String> components = Arrays.asList(
            safe(address.getAddressLine2()),   // Provincia
            safe(address.getTown()),           // Distrito
            safe(address.getAddressLine1()),   // Corregimiento
            safe(address.getArea()),           // Barrio
            safe(address.getStreet()),         // Calle
            safe(address.getAddressLine3())    // Casa
        );

        String concatenated = components.stream()
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.joining(" "));

        log.debug("Dirección concatenada: {}", concatenated);
        return concatenated.length() > 100 ? concatenated.substring(0, 100) : concatenated;
    } else {
        return safe(address.getStreet());
    }
}


    private static String safe(String value) {
        return value != null ? value.trim() : "";
    }
}
