package com.millicom.workflow.connector.contact.client;

import java.util.List;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PatchMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;

import com.millicom.workflow.connector.contact.client.configuration.GalaxionHeadersConfiguration;
import com.millicom.workflow.connector.contact.client.domain.request.ContactRequestDTO;
import com.millicom.workflow.connector.contact.client.domain.request.EmailRequestDTO;
import com.millicom.workflow.connector.contact.client.domain.request.UpdateAddressRequestDTO;
import com.millicom.workflow.connector.contact.client.domain.request.UpdateCommunicationPreferenceRequestDTO;
import com.millicom.workflow.connector.contact.client.domain.request.UpdatePermissionRequestDTO;
import com.millicom.workflow.connector.contact.client.domain.response.AddressResponseDTO;
import com.millicom.workflow.connector.contact.client.domain.response.ContactResponseDTO;
import com.millicom.workflow.connector.contact.client.domain.response.permissions.PermissionGroupListResponseDTO;

@FeignClient(name = "ContactManagementClient", url = "${webservice.contact.url}", path = "/api/v2", configuration = GalaxionHeadersConfiguration.class)
public interface ContactClient {

    @GetMapping("/contacts/{contact_uuid}/addresses")
    List<AddressResponseDTO> getAddresses(@PathVariable("contact_uuid") String contactUuid);

    @GetMapping("/contacts/{contact_uuid}")
    ContactResponseDTO getContact(@PathVariable("contact_uuid") String contactUuid);

    @PostMapping("/contacts")
    String createContact(@RequestBody ContactRequestDTO contactRequest);

    @PatchMapping(value = "/emails/{email_id}", consumes = "application/merge-patch+json")
    void updateContactEmail(@PathVariable("email_id") Long emailId, @RequestBody EmailRequestDTO emailRequestDto);

    @PostMapping("/contacts/{contact_uuid}/emails")
    void createContactEmail(@PathVariable("contact_uuid") String contactUuid, @RequestBody EmailRequestDTO emailRequestDTO);

    @PutMapping("/contacts/{contact_uuid}/anonymize")
    void anonymizeContact(@PathVariable("contact_uuid") String contactUuid);

    @PutMapping("/contacts/{contact_uuid}/communication-preferences")
    Long updateCommunicationPreferences(@PathVariable("contact_uuid") String contactUuid, @RequestBody UpdateCommunicationPreferenceRequestDTO updateCommunicationPreferenceRequestDTO);

    @PatchMapping(value = "/contacts/{contact_uuid}/addresses", consumes = "application/merge-patch+json")
    void updateAddress(@PathVariable("contact_uuid") String contactUuid, @RequestBody UpdateAddressRequestDTO updateAddressRequestDTO);

    @PutMapping("/contacts/{contact_uuid}/permissions")
    void updatePermissions(@PathVariable("contact_uuid") String contactUuid, @RequestBody UpdatePermissionRequestDTO updatePermissionRequestDTO);

    @GetMapping("/contacts/{contact_uuid}/permissions")
    PermissionGroupListResponseDTO findPermissions(@PathVariable("contact_uuid") String contactUuid);
}
