package com.millicom.workflow.connector.contact.domain.request;

import java.util.List;

import com.millicom.workflow.spi.com.dto.InvoiceSettingsDTO;
import com.millicom.workflow.spi.com.dto.v1.ContactDTO;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
public class CreateContactsRequest {

    private String correlationId;
    private String customerReference;
    private String uuid;
    private InvoiceSettingsDTO invoiceSettings;
    private List<ContactDTO> contacts;
}
