package com.millicom.workflow.connector.contact.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.millicom.workflow.connector.contact.client.ContactClient;
import com.millicom.workflow.connector.contact.client.domain.request.EmailRequestDTO;
import com.millicom.workflow.connector.contact.domain.dto.ContactDTO;
import com.millicom.workflow.connector.contact.domain.dto.EmailDTO;
import com.millicom.workflow.connector.contact.domain.dto.OfferDTO;
import com.millicom.workflow.connector.contact.domain.request.ValidateEmailRequest;
import com.millicom.workflow.connector.contact.domain.response.ValidateEmailResponse;
import com.millicom.workflow.connector.contact.enums.EmailTypeEnum;
import com.millicom.workflow.connector.contact.enums.SendByTypeEnum;
import com.millicom.workflow.connector.contact.exception.ContactConnectorException;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class ValidateEmailService {

    private static final String REMOVE_PAPER_INVOICE = "removePaperInvoice";
    private final ContactClient contactClient;


    public ValidateEmailResponse callValidateEmail(ValidateEmailRequest validateEmailRequest) {
        try {

            verifyContactEmails(validateEmailRequest, validateEmailRequest.getCorrelationId(), () ->
                    validateEmailRequest.getPayer() == null ? new ArrayList<>() : Collections.singletonList(validateEmailRequest.getPayer()));
            verifyContactEmails(validateEmailRequest, validateEmailRequest.getCorrelationId(), () ->
                    validateEmailRequest.getOwner() == null ? new ArrayList<>() : Collections.singletonList(validateEmailRequest.getOwner()));
            verifyContactEmails(validateEmailRequest, validateEmailRequest.getCorrelationId(), () ->
                    validateEmailRequest.getCart().getOffers() == null ? new ArrayList<>() : validateEmailRequest.getCart().getOffers().stream().map(OfferDTO::getContact).collect(Collectors.toList()));

            Map<String, Object> flowData = new HashMap<>();
            flowData.put(REMOVE_PAPER_INVOICE, isRemovePaperInvoice(validateEmailRequest));

            return new ValidateEmailResponse(flowData);

        } catch (Exception e) {
            log.error(e.getMessage());
            throw new ContactConnectorException(String.valueOf(e));
        }
    }

    void verifyContactEmails(ValidateEmailRequest validateEmailRequest, String orderId, ValidateEmailService.ContactsProvider contactsProvider) {
        for (ContactDTO contact : contactsProvider.provide()) {
            if (contact == null || StringUtils.isEmpty(contact.getContactUuid()) || contact.getEmails() == null) {
                continue;
            }

            boolean invalidEmails = contact.getEmails().stream()
                    .anyMatch(email -> email != null && StringUtils.isEmpty(email.getEmail()));

            if (invalidEmails) {
                log.error("Customer {} has invalid emails in the contact with id {}", validateEmailRequest.getCustomerReference(), contact.getContactUuid());
                throw new ContactConnectorException(String.format("Customer %s has invalid emails in the contact with id %s", validateEmailRequest.getCustomerReference(), contact.getContactUuid()));
            }

            for(EmailDTO mail : contact.getEmails()) {
                EmailRequestDTO emailToValidate = toEmailRequest(mail);
                log.info("[orderId: {}]Send UpdateContactEmailRequestDTO to contact management {} with mail id {}", orderId, emailToValidate, mail.getId());
                contactClient.updateContactEmail(mail.getId(), emailToValidate);
            }
        }
    }

    boolean isRemovePaperInvoice(ValidateEmailRequest validateEmailRequest) {
        ContactDTO payer = validateEmailRequest.getPayer();
        if (payer == null || payer.getEmails() == null) {
            return false;
        }

        if (validateEmailRequest.getInvoiceSettings() !=null && SendByTypeEnum.POSTAL.name().equals(validateEmailRequest.getInvoiceSettings().getSendBy())) {
            return false;
        }

        return payer.getEmails().stream()
                .filter(Objects::nonNull)
                .anyMatch(payerEmail -> payerEmail.getValidated() != null && payerEmail.getValidated());
    }

    private EmailRequestDTO toEmailRequest(EmailDTO emailDTO) {
        EmailTypeEnum emailType = emailDTO.getType() == null ? EmailTypeEnum.MAIN : EmailTypeEnum.valueOf(emailDTO.getType());
        return EmailRequestDTO.builder()
                .validated(true)
                .email(emailDTO.getEmail())
                .type(emailType.name())
                .build();
    }

    @FunctionalInterface
    interface ContactsProvider {
        List<ContactDTO> provide();
    }
}
