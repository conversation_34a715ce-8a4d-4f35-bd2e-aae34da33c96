package com.millicom.workflow.connector.contact.stream.listener;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import mc.monacotelecom.workflow.connector.common.service.CommonIntegrationService;

import static com.millicom.workflow.connector.contact.stream.channel.ContactConnectorChannels.WKF_CONTACT_UPDATE_MARKETING_PREFS;

import org.activiti.cloud.api.process.model.IntegrationRequest;
import org.springframework.cloud.stream.annotation.StreamListener;
import org.springframework.stereotype.Component;

import com.millicom.workflow.connector.contact.domain.request.UpdateMarketingPrefsRequest;
import com.millicom.workflow.connector.contact.service.UpdateMarketingPrefsService;

@Slf4j
@Component
@RequiredArgsConstructor
public class UpdateMarketingPrefsListener {

    private final CommonIntegrationService commonIntegrationService;
    private final UpdateMarketingPrefsService updateMarketingPrefsService;

    @StreamListener(WKF_CONTACT_UPDATE_MARKETING_PREFS)
    public void updateMarketingPrefsListener(IntegrationRequest event) {
        var request = commonIntegrationService.getListenerInput(event, UpdateMarketingPrefsRequest.class);

        updateMarketingPrefsService.callUpdateMarketingPrefs(request);

        commonIntegrationService.sendResult(event);
    }
}