package com.millicom.workflow.connector.contact.domain.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;

@Data
@NoArgsConstructor
@AllArgsConstructor
//TO DO
/**
 * to remove once EnrichContactService has been removed
 */
public class PersonDTO {

    private String firstName;
    private String lastName;
    private String title;
    private LocalDate birthDate;
    private String nationality;

}
