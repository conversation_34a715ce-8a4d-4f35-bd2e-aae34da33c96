package com.millicom.workflow.connector.contact.client.domain.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import java.time.LocalDate;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class IdentityDocumentRequestDTO {

    private Boolean mainIdentityDocument;
    private String identifier;
    @NotBlank
    private String type;
    private String nationality;
    private LocalDate expireAt;
}
