package com.millicom.workflow.connector.contact.domain.request;

import com.millicom.workflow.connector.contact.domain.dto.CustomerCommunicationSettingsDTO;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
public class UpdateCommunicationSettingsRequest {

    private String correlationId;
    private String contactUuid;
    private CustomerCommunicationSettingsDTO communicationSettings;

}
