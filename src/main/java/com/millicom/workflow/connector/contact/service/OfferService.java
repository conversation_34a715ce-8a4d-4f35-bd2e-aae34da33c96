////this class handles all processing made to offer in this connector through
////contactOfferProcessing
//// As for the sake of responsibility isolation, offer Handling must not be involved in the context of contact creation.
//// this code is stored for future updates
package com.millicom.workflow.connector.contact.service;
//import fr.njj.galaxion.workflow.contact.client.ContactClient;
//import fr.njj.galaxion.workflow.contact.client.dto.request.ContactRequestDTO;
//import fr.njj.galaxion.workflow.contact.client.dto.request.CreateAddressRequestDTO;
//import fr.njj.galaxion.workflow.contact.client.dto.request.EmailRequestDTO;
//import fr.njj.galaxion.workflow.contact.client.dto.request.IdentityDocumentRequestDTO;
//import fr.njj.galaxion.workflow.contact.client.dto.request.PermissionGroupListRequestDTO;
//import fr.njj.galaxion.workflow.contact.client.dto.request.PermissionGroupRequestDTO;
//import fr.njj.galaxion.workflow.contact.client.dto.request.PermissionRequestDTO;
//import fr.njj.galaxion.workflow.contact.client.dto.request.PhoneNumberRequestDTO;
//import fr.njj.galaxion.workflow.contact.client.dto.response.AddressResponseDTO;
//import fr.njj.galaxion.workflow.contact.enums.CommunicationChannelEnum;
//import fr.njj.galaxion.workflow.contact.exception.ContactConnectorException;
//import fr.njj.galaxion.workflow.contact.domain.dto.ContactAddressDTO;
//import fr.njj.galaxion.workflow.contact.domain.dto.ContactDTO;
//import fr.njj.galaxion.workflow.contact.domain.dto.CustomerCommunicationSettingsDTO;
//import fr.njj.galaxion.workflow.contact.domain.dto.CustomerDTO;
//import fr.njj.galaxion.workflow.contact.domain.dto.CustomerIdentityDocumentDTO;
//import fr.njj.galaxion.workflow.contact.domain.dto.OfferDTO;
//import fr.njj.galaxion.workflow.contact.domain.request.CreateContactRequest;
//import fr.njj.galaxion.workflow.contact.value.CommunicationPreferenceDTO;
//import lombok.AllArgsConstructor;
//import lombok.extern.slf4j.Slf4j;
//import mc.monacotelecom.common.business.enums.AddressTypeEnum;
//import org.apache.commons.lang3.EnumUtils;
//import org.apache.commons.lang3.StringUtils;
//import org.springframework.stereotype.Service;
//
//import java.time.LocalDateTime;
//import java.util.ArrayList;
//import java.util.List;
//import java.util.Locale;
//import java.util.Objects;
//import java.util.Optional;
//import java.util.Set;
//import java.util.stream.Collectors;
//
//import static fr.njj.galaxion.workflow.contact.mappers.AddressMapper.toCreateRequest;
//import static fr.njj.galaxion.workflow.contact.mappers.ContactEmailMapper.toEmailRequestList;
//import static fr.njj.galaxion.workflow.contact.mappers.ContactPhoneNumberMapper.toContactPhoneNumberDTOSet;
//import static fr.njj.galaxion.workflow.contact.mappers.IdentityDocumentsMapper.toIdentityDocumentDTOList;
//import static fr.njj.galaxion.workflow.contact.service.ContactCreationService.MOBILE;
//
//
//@Slf4j
//@Service
//@AllArgsConstructor
public class OfferService {
//
//    private final ContactClient contactClient;
//
//
//    public void contactOfferProcessing(CreateContactRequest createContactRequest){
//        List<String> errorMessages = new ArrayList<>();
//        CustomerDTO customer = Optional.ofNullable(createContactRequest.getCustomer()).orElseThrow(() -> new ContactConnectorException(String.format("could not find customer for order with Id %s", createContactRequest.getCorrelationId())));
//
//        if (Objects.nonNull(createContactRequest.getCart().getOffers())) {
//            createContactRequest.getCart().getOffers()
//                    .stream()
//                    .filter(offer -> Objects.nonNull(offer.getContact()) && offer.getContact().getCreationDate() == null)
//                    .forEach(offer -> {
//                        try {
//                            customerNumberForOfferSetter(offer, customer);
//                            ContactRequestDTO createContactRequestDTO = getUserLineCreateContactRequest(offer.getContact(), customer);
//                            createContactRequestDTO.setUuid(null); //force create new one
//                            String uuid = contactClient.createContact(createContactRequestDTO);
//                            offer.getContact().setContactUuid(uuid);
//                            offer.getContact().setCreationDate(LocalDateTime.now());
//                            log.info("Contact userLine created {}", uuid);
//                        } catch (Exception e) {
//                            log.info("couldn't create the userLine account for order {}", createContactRequest.getCorrelationId());
//                            errorMessages.add(String.format("couldn't create the userLine order %s", createContactRequest.getCorrelationId()));
//                            log.error("error happened: ", e);
//                        }
//                    });
//        } else {
//            log.info("Order does not contain offer(s)");
//        }
//
//        createContactRequest.getCart().getOffers()
//                .stream()
//                .filter(offer -> Objects.nonNull(offer.getContact()) && offer.getContact().getCreationDate() == null)
//                .forEach(offer -> {
//                    try {
//                        customerNumberForOfferSetter(offer, customer);
//                        ContactRequestDTO createContactRequestDTO = getUserLineCreateContactRequest(offer.getContact(), customer);
//                        createContactRequestDTO.setUuid(null); //force create new one
//                        String uuid = contactClient.createContact(createContactRequestDTO);
//                        offer.getContact().setContactUuid(uuid);
//                        offer.getContact().setCreationDate(LocalDateTime.now());
//                    } catch (Exception e) {
//                        log.info("couldn't create the userLine account for order {}", createContactRequest.getCorrelationId());
//                        errorMessages.add(String.format("couldn't create the userLine account order %s", createContactRequest.getCorrelationId()));
//                        log.error("error happened: ", e);
//                    }
//                });
//
//    }
//
//
//    private void customerNumberForOfferSetter(OfferDTO offer, CustomerDTO customer){
//       offer.getContact().setCustomerNumber(customer.getCustomerReference());
//    }
//
//    private void decorateOfferAddressId(String contactUuid, List<OfferDTO> offers) {
//        var addressesResponseDTO = contactClient.getAddresses(contactUuid);
//        var deliveryAddress = findDeliveryAddress(addressesResponseDTO);
//        offers.forEach(o -> o.getContact().getAddress().setId(deliveryAddress.getId()));
//    }
//
//    AddressResponseDTO findDeliveryAddress(List<AddressResponseDTO> addresses) {
//        return addresses.stream()
//                .filter(a -> AddressTypeEnum.DELIVERY.equals(a.getType()))
//                .findAny()
//                .orElseThrow(() -> new ContactConnectorException(String.format("Not found %s address.", AddressTypeEnum.DELIVERY)));
//    }
//
//    public ContactRequestDTO getUserLineCreateContactRequest(ContactDTO offerContact, CustomerDTO customer) {
//        ContactRequestDTO request = getCreateContactRequest(offerContact, AddressTypeEnum.INSTALLATION);
//        request.setCommunicationPreference(getDefaultCommunicationSettings(customer));
//        return request;
//    }
//
//    public ContactRequestDTO getCreateContactRequest(ContactDTO contactDTO, mc.monacotelecom.common.business.enums.AddressTypeEnum addressType) {
//        List<EmailRequestDTO> emails = toEmailRequestList(contactDTO.getEmails()).stream()
//                .filter(email -> StringUtils.isNotBlank(email.getEmail()))
//                .collect(Collectors.toList());
//        List<PhoneNumberRequestDTO> phoneNumbers = toContactPhoneNumberDTOSet(contactDTO.getNumbers()).stream()
//                .filter(number -> StringUtils.isNotBlank(number.getPhoneNumber()))
//                .collect(Collectors.toList());
//        List<CreateAddressRequestDTO> addresses = new ArrayList<>();
//        ContactAddressDTO address = contactDTO.getAddress();
//        if (address != null) {
//            addresses.add(toCreateRequest(address, addressType));
//        }
//        PermissionGroupListRequestDTO permissionGroupList = getPermissionGroupListRequest(contactDTO, true);
//        List<IdentityDocumentRequestDTO> identityDocuments = new ArrayList<>();
//        if (contactDTO.getIdentityDocuments() != null && !contactDTO.getIdentityDocuments().isEmpty()) {
//            Set<CustomerIdentityDocumentDTO> collect = contactDTO.getIdentityDocuments().stream().filter(document -> document.getType() != null && document.getIdentifier() != null).collect(Collectors.toSet());
//            identityDocuments = toIdentityDocumentDTOList(collect);
//
//        }
//        return ContactRequestDTO.builder()
//                .uuid(contactDTO.getContactUuid())
//                .emails(emails)
//                .phoneNumbers(phoneNumbers)
//                .identityDocuments(identityDocuments)
//                .firstName(contactDTO.getPerson().getFirstName())
//                .lastName(contactDTO.getPerson().getLastName())
//                .title(contactDTO.getPerson().getTitle())
//                .birthDate(contactDTO.getPerson().getBirthDate())
//                .nationality(contactDTO.getPerson().getNationality())
//                .addresses(addresses)
//                //TODO to testing
//                .permissionGroupList(permissionGroupList)
//                .gdprValidationDate(contactDTO.getGdprValidationDate())
//                .gdprLanguage(Objects.nonNull(contactDTO.getGdprLanguage()) ? contactDTO.getGdprLanguage() : Locale.getDefault())
//                .allowThirdParties(contactDTO.getAllowThirdParties())
//                .build();
//    }
//
//
//    public CommunicationPreferenceDTO getDefaultCommunicationSettings(CustomerDTO customerDTO) {
//        if (Objects.nonNull(customerDTO.getCommunicationSettings()) && !StringUtils.isEmpty(customerDTO.getCommunicationSettings().getPreferredChannel()) && !StringUtils.isEmpty(customerDTO.getCommunicationSettings().getLanguage())) {
//            return getCommunicationSettings(customerDTO.getCommunicationSettings(), customerDTO.getCommunicationSettings().getLanguage());
//        } else {
//            return null;
//        }
//    }
//
//    public CommunicationPreferenceDTO getCommunicationSettings(CustomerCommunicationSettingsDTO settings, String communicationLanguage) {
//        CommunicationPreferenceDTO communicationPreferenceDTO = new CommunicationPreferenceDTO();
//        String commChannel = settings.getPreferredChannel();
//
//        if (MOBILE.equals(commChannel)) {
//            commChannel = CommunicationChannelEnum.MOBILE_NUMBER.name();
//        }
//        if (StringUtils.isNotBlank(commChannel) && EnumUtils.isValidEnum(CommunicationChannelEnum.class, commChannel)) {
//            communicationPreferenceDTO.setChannel(CommunicationChannelEnum.valueOf(commChannel));
//        }
//
//        if (settings.getEmail() != null) {
//            communicationPreferenceDTO.setEmail(StringUtils.trimToNull(settings.getEmail()));
//        }
//        communicationPreferenceDTO.setLanguage(Locale.forLanguageTag(communicationLanguage));
//        communicationPreferenceDTO.setPhoneNumber(StringUtils.trimToNull(settings.getNumber()));
//        return communicationPreferenceDTO;
//    }
//
//    public PermissionGroupListRequestDTO getPermissionGroupListRequest(ContactDTO contactDTO, boolean isEnabled) {
//        List<PermissionGroupRequestDTO> permissionGroups = getPermissionGroups(contactDTO, isEnabled);
//
//        PermissionGroupListRequestDTO permissionGroupList = new PermissionGroupListRequestDTO();
//        permissionGroupList.setPermissionGroups(permissionGroups);
//        permissionGroupList.setAllowThirdParty(contactDTO.getAllowThirdParties());
//        return permissionGroupList;
//    }
//
//    public List<PermissionGroupRequestDTO> getPermissionGroups(ContactDTO contactDTO, boolean isEnabled) {
//        List<PermissionGroupRequestDTO> permissionGroups = new ArrayList<>();
//        contactDTO.getEnabledPermissions().forEach((permissionGroupEnum, permissionEnums) -> {
//            PermissionGroupRequestDTO permissionGroupRequestDTO = new PermissionGroupRequestDTO();
//            permissionGroupRequestDTO.setPermissionGroup(permissionGroupEnum);
//            permissionEnums.forEach(permissionEnum -> permissionGroupRequestDTO.getPermissions().add(new PermissionRequestDTO(permissionEnum, isEnabled)));
//            permissionGroups.add(permissionGroupRequestDTO);
//        });
//        return permissionGroups;
//    }
}
