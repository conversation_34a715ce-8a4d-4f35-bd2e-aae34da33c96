package com.millicom.workflow.connector.contact.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import com.millicom.workflow.connector.contact.client.ContactClient;
import com.millicom.workflow.connector.contact.client.domain.dto.PermissionDTO;
import com.millicom.workflow.connector.contact.client.domain.request.UpdatePermissionRequestDTO;
import com.millicom.workflow.connector.contact.domain.request.UpdateMarketingPrefsRequest;
import com.millicom.workflow.connector.contact.enums.PermissionGroupEnum;

import java.util.SortedSet;
import java.util.TreeSet;

@Slf4j
@Service
@RequiredArgsConstructor
public class UpdateMarketingPrefsService {

    private final ContactClient contactClient;

    public void callUpdateMarketingPrefs(UpdateMarketingPrefsRequest updateMarketingPrefsRequest) {
        UpdatePermissionRequestDTO activeCustomerPermissions = getPermissionRequestFor(PermissionGroupEnum.ACTIVE_CUSTOMER, updateMarketingPrefsRequest);
        if (activeCustomerPermissions != null) {
            contactClient.updatePermissions(updateMarketingPrefsRequest.getOwnerUuid(), activeCustomerPermissions);
        }

        UpdatePermissionRequestDTO noLongerCustomerPermissions = getPermissionRequestFor(PermissionGroupEnum.NO_LONGER_CUSTOMER, updateMarketingPrefsRequest);
        if (noLongerCustomerPermissions != null) {
            contactClient.updatePermissions(updateMarketingPrefsRequest.getOwnerUuid(), noLongerCustomerPermissions);
        }
    }

    private UpdatePermissionRequestDTO getPermissionRequestFor(PermissionGroupEnum permissionGroup, UpdateMarketingPrefsRequest updateMarketingPrefsRequest) {
        SortedSet<PermissionDTO> permissions = new TreeSet<>();
        if (updateMarketingPrefsRequest.getEnabledPermissions().containsKey(permissionGroup)) {
            updateMarketingPrefsRequest.getEnabledPermissions().get(permissionGroup).stream()
                    .map(permissionEnum -> new PermissionDTO(
                            permissionEnum,
                            true
                    ))
                    .forEach(permissions::add);
        }
        if (updateMarketingPrefsRequest.getDisabledPermissions().containsKey(permissionGroup)) {
            updateMarketingPrefsRequest.getDisabledPermissions().get(permissionGroup).stream()
                    .map(permissionEnum -> new PermissionDTO(
                            permissionEnum,
                            true
                    ))
                    .forEach(permissions::add);
        }
        if (permissions.isEmpty()) {
            return null;
        }
        return new UpdatePermissionRequestDTO(permissionGroup, permissions);
    }
}
