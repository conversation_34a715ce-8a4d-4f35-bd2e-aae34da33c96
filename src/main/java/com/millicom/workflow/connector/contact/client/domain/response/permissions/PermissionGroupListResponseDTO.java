package com.millicom.workflow.connector.contact.client.domain.response.permissions;


import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class PermissionGroupListResponseDTO {
    private Boolean allowThirdParty;
    private List<PermissionGroupResponseDTO> permissionGroupResponseDTO = new ArrayList<>();
}
