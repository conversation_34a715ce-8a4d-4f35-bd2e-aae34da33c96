package com.millicom.workflow.connector.contact.client.domain.request;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

import com.millicom.workflow.connector.contact.enums.PermissionEnum;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class PermissionRequestDTO {

    @NotNull
    private PermissionEnum permission;
    private Boolean enabled;
}