package com.millicom.workflow.connector.contact.domain.dto;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.EnumMap;
import java.util.EnumSet;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Set;

import com.millicom.workflow.connector.contact.enums.AccountTypeEnum;
import com.millicom.workflow.connector.contact.enums.PermissionEnum;
import com.millicom.workflow.connector.contact.enums.PermissionGroupEnum;

@Getter
@Setter
public class ContactDTO {

    private String contactUuid;
    private LocalDateTime creationDate;
    private String customerNumber;
    private Set<EmailDTO> emails;
    private Set<PhoneNumberDTO> numbers;
    private List<ContactAddressDTO> addresses;
    private Boolean allowThirdParties;
    private Boolean hasMarketingProfiling;
    private String companyPosition;
    private LocalDate GdprValidationDate;
    private Locale GdprLanguage;
    private Map<PermissionGroupEnum, EnumSet<PermissionEnum>> enabledPermissions = new EnumMap<>(PermissionGroupEnum.class);
    private Map<PermissionGroupEnum, EnumSet<PermissionEnum>> disabledPermissions = new EnumMap<>(PermissionGroupEnum.class);
    private Set<CustomerIdentityDocumentDTO> identityDocuments;
    private PersonDTO person;
    private AccountTypeEnum type;
    private String subType;
    private String brand;
}
