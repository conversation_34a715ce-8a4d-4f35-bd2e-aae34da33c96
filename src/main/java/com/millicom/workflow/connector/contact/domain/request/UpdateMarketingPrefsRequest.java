package com.millicom.workflow.connector.contact.domain.request;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.EnumMap;
import java.util.EnumSet;
import java.util.Map;

import com.millicom.workflow.connector.contact.enums.PermissionEnum;
import com.millicom.workflow.connector.contact.enums.PermissionGroupEnum;

@Getter
@Setter
@NoArgsConstructor
public class UpdateMarketingPrefsRequest {


    private String ownerUuid;
    private Map<PermissionGroupEnum, EnumSet<PermissionEnum>> enabledPermissions = new EnumMap<>(PermissionGroupEnum.class);
    private Map<PermissionGroupEnum, EnumSet<PermissionEnum>> disabledPermissions = new EnumMap<>(PermissionGroupEnum.class);
}
