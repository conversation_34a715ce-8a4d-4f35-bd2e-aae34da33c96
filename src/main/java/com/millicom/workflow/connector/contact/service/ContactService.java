package com.millicom.workflow.connector.contact.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import com.millicom.workflow.connector.contact.client.ContactClient;
import com.millicom.workflow.connector.contact.domain.request.AnonymizeContactRequest;

@Slf4j
@Service
@RequiredArgsConstructor
public class ContactService {

    private final ContactClient contactClient;

    public void callForgetContact(AnonymizeContactRequest anonymizeContactRequest) {
        contactClient.anonymizeContact(anonymizeContactRequest.getContactUuid());
        log.info("[ORDER_ID={}] Request to anonymize owner with contactUuid {} was sent", anonymizeContactRequest.getCorrelationId(), anonymizeContactRequest.getContactUuid());
    }
}
