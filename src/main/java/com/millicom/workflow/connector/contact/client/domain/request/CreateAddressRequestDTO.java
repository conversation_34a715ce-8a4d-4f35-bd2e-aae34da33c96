package com.millicom.workflow.connector.contact.client.domain.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


import javax.validation.constraints.NotNull;

import com.millicom.workflow.spi.com.dto.v1.enumeration.AddressTypeEnum;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class CreateAddressRequestDTO {

    @NotNull
    private AddressTypeEnum type;
    private String addressLine1;
    private String addressLine2;
    private String addressLine3;
    private String town;
    private String county;
    private String code;
    private String poBox;
    private String area;
    private String country;
    private String street;
    private String streetNumber;
    private String streetQualifier;
    private String buildingType;
    private String buildingBlock;
    private String flatNumber;
}
