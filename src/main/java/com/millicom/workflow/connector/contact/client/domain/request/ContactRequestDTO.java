package com.millicom.workflow.connector.contact.client.domain.request;

import com.fasterxml.jackson.annotation.JsonAlias;
import com.millicom.workflow.connector.contact.client.domain.dto.CommunicationPreferenceDTO;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.PastOrPresent;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Comparator;
import java.util.List;
import java.util.Locale;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ContactRequestDTO implements Comparable<ContactRequestDTO> {

    private String uuid;

    private String contactType;

    @NotBlank
    private String firstName;

    @NotBlank
    private String lastName;

    private String title;

    private LocalDate birthDate;

    private String nationality;

    private String companyPosition;

    private List<@Valid EmailRequestDTO> emails;

    private List<@Valid PhoneNumberRequestDTO> phoneNumbers;

    private List<@Valid CreateAddressRequestDTO> addresses;

    private List<@Valid IdentityDocumentRequestDTO> identityDocuments;

    @JsonAlias("permissions")
    private @Valid PermissionGroupListRequestDTO permissionGroupList;

    private Boolean allowThirdParties;

    @PastOrPresent
    private LocalDateTime gdprValidationDate;

    private Locale gdprLanguage;

    @Valid
    private CommunicationPreferenceDTO communicationPreference;

    private Boolean vip;

    private LocalDateTime idLastCheckedAt;

    @Override
    public int compareTo(ContactRequestDTO contact) {
        return Comparator.comparing(ContactRequestDTO::getFirstName, Comparator.nullsFirst(Comparator.naturalOrder()))
                .thenComparing(ContactRequestDTO::getLastName, Comparator.nullsFirst(Comparator.naturalOrder()))
                .thenComparing(ContactRequestDTO::getBirthDate, Comparator.nullsFirst(Comparator.naturalOrder()))
                .thenComparing(ContactRequestDTO::getNationality, Comparator.nullsFirst(Comparator.naturalOrder()))
                .compare(this, contact);
    }
}
