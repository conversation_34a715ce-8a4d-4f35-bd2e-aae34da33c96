package com.millicom.workflow.connector.contact.mappers;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.stream.Collectors;

import com.millicom.workflow.connector.contact.client.domain.request.PhoneNumberRequestDTO;
import com.millicom.workflow.spi.com.dto.v1.PhoneNumberDTO;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class ContactPhoneNumberMapper {

    public static PhoneNumberRequestDTO toContactPhoneNumberDTO(PhoneNumberDTO phoneNumberDTO) {
        return PhoneNumberRequestDTO.builder()
                .phoneNumber(phoneNumberDTO.getPhoneNumber())
                .type(phoneNumberDTO.getType())
                .build();
    }

    public static List<PhoneNumberRequestDTO> toContactPhoneNumberDTOList(List<PhoneNumberDTO> phoneNumberDTOSet) {
        return phoneNumberDTOSet.stream()
                .map(ContactPhoneNumberMapper::toContactPhoneNumberDTO)
                .collect(Collectors.toList());
    }
}
