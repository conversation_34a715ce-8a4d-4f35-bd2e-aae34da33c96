package com.millicom.workflow.connector.contact.service;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import com.millicom.workflow.connector.contact.client.ContactClient;
import com.millicom.workflow.connector.contact.client.domain.request.UpdateCommunicationPreferenceRequestDTO;
import com.millicom.workflow.connector.contact.domain.request.UpdateCommunicationSettingsRequest;
import com.millicom.workflow.connector.contact.domain.response.UpdateCommunicationSettingsResponse;
import com.millicom.workflow.connector.contact.enums.CommunicationChannelEnum;

import java.util.Locale;

@Service
@RequiredArgsConstructor
public class UpdateCommunicationSettingsService {

    public static final String MOBILE = "MOBILE";
    private final ContactClient contactClient;

    public UpdateCommunicationSettingsResponse callUpdateCommunicationSettings(UpdateCommunicationSettingsRequest updateCommunicationSettingsRequest) {
        var communicationSettings = updateCommunicationSettingsRequest.getCommunicationSettings();

        // #SPI_V2 #EPIC should not be needed anymore?
        String commChannel = communicationSettings.getPreferredChannel();
        if (MOBILE.equals(commChannel)) {
            commChannel = CommunicationChannelEnum.MOBILE_NUMBER.name();
        }

        UpdateCommunicationPreferenceRequestDTO updateCommunicationPreferenceRequestDTO = new UpdateCommunicationPreferenceRequestDTO(
                CommunicationChannelEnum.valueOf(commChannel),
                Locale.forLanguageTag(communicationSettings.getLanguage()),
                communicationSettings.getEmail(),
                communicationSettings.getNumber()
        );
        long communicationPreferenceId = this.contactClient.updateCommunicationPreferences(updateCommunicationSettingsRequest.getContactUuid(), updateCommunicationPreferenceRequestDTO);

        return new UpdateCommunicationSettingsResponse(communicationPreferenceId);
    }
}
