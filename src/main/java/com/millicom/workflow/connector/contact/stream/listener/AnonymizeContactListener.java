package com.millicom.workflow.connector.contact.stream.listener;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import mc.monacotelecom.workflow.connector.common.service.CommonIntegrationService;

import static com.millicom.workflow.connector.contact.stream.channel.ContactConnectorChannels.WKF_CONTACT_ANONYMIZE;
import static com.millicom.workflow.connector.contact.stream.channel.ContactConnectorChannels.WKF_CONTACT_FORGET_ADDRESS;
import static com.millicom.workflow.connector.contact.stream.channel.ContactConnectorChannels.WKF_CONTACT_FORGET_COMM_SETTINGS;
import static com.millicom.workflow.connector.contact.stream.channel.ContactConnectorChannels.WKF_CONTACT_FORGET_CONTACTS;
import static com.millicom.workflow.connector.contact.stream.channel.ContactConnectorChannels.WKF_CONTACT_FORGET_EMAIL;

import org.activiti.cloud.api.process.model.IntegrationRequest;
import org.springframework.cloud.stream.annotation.StreamListener;
import org.springframework.stereotype.Component;

import com.millicom.workflow.connector.contact.domain.request.AnonymizeContactRequest;
import com.millicom.workflow.connector.contact.service.ContactService;

@Slf4j
@Component
@AllArgsConstructor
public class AnonymizeContactListener {

    private final ContactService contactService;
    private final CommonIntegrationService commonIntegrationService;

    @StreamListener(value = WKF_CONTACT_ANONYMIZE)
    public void anonymize(IntegrationRequest event) {
        var request = commonIntegrationService.getListenerInput(event, AnonymizeContactRequest.class);

        contactService.callForgetContact(request);

        commonIntegrationService.sendResult(event);
    }

    @StreamListener(value = WKF_CONTACT_FORGET_ADDRESS)
    public void forgetAddress(IntegrationRequest event) {
        anonymize(event);
    }

    @StreamListener(value = WKF_CONTACT_FORGET_COMM_SETTINGS)
    public void forgetCommunicationSettings(IntegrationRequest event) {
        anonymize(event);
    }

    @StreamListener(value = WKF_CONTACT_FORGET_CONTACTS)
    public void forgetContact(IntegrationRequest event) {
        anonymize(event);
    }

    @StreamListener(value = WKF_CONTACT_FORGET_EMAIL)
    public void forgetEmail(IntegrationRequest event) {
        anonymize(event);
    }
}

