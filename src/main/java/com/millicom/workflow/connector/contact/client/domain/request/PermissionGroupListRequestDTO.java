package com.millicom.workflow.connector.contact.client.domain.request;

import com.fasterxml.jackson.annotation.JsonAlias;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class PermissionGroupListRequestDTO {

    private Boolean allowThirdParty;

    private Boolean hasMarketingProfiling;

    @JsonAlias("permissionGroupResponse")
    private List<PermissionGroupRequestDTO> permissionGroups;
}
