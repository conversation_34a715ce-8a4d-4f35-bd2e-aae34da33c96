package com.millicom.workflow.connector.contact.mappers;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.stream.Collectors;

import com.millicom.workflow.connector.contact.client.domain.request.IdentityDocumentRequestDTO;
import com.millicom.workflow.spi.com.dto.v1.IdentityDocumentDTO;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class IdentityDocumentsMapper {

    public static IdentityDocumentRequestDTO toIdentityDocumentDTO(IdentityDocumentDTO customerIdentityDocumentDTO) {
        return IdentityDocumentRequestDTO.builder()
                .mainIdentityDocument(customerIdentityDocumentDTO.getMainIdentityDocument())
                .identifier(customerIdentityDocumentDTO.getIdentifier())
                .type(customerIdentityDocumentDTO.getType())
                .nationality(customerIdentityDocumentDTO.getNationality())
                .build();
    }

    public static List<IdentityDocumentRequestDTO> toIdentityDocumentDTOList(
            List<IdentityDocumentDTO> customerIdentityDocumentDTOSet) {
        return customerIdentityDocumentDTOSet.stream()
                .map(IdentityDocumentsMapper::toIdentityDocumentDTO)
                .collect(Collectors.toList());
    }
}
