package com.millicom.workflow.connector.contact.domain.request;

import com.millicom.workflow.connector.contact.domain.dto.ContactDTO;
import com.millicom.workflow.connector.contact.domain.dto.CustomerCommunicationSettingsDTO;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
public class EnrichContactRequest {

    private String correlationId;
    private CustomerCommunicationSettingsDTO communicationSettings;
    private ContactDTO payer;
    private ContactDTO owner;
}

