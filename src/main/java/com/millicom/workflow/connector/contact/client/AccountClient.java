package com.millicom.workflow.connector.contact.client;

import java.util.List;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;

import com.millicom.workflow.connector.contact.client.configuration.GalaxionHeadersConfiguration;
import com.millicom.workflow.connector.contact.client.domain.response.AccountContactResponseDTO;

@FeignClient(name = "AccountClient", url = "${webservice.account.url}", path = "/api/v3", configuration = GalaxionHeadersConfiguration.class)
public interface AccountClient {

    @GetMapping(value = "/accounts/{account_id}/contacts")
    List<AccountContactResponseDTO> getAccountContacts(@PathVariable("account_id") String accountId);

}
