package com.millicom.workflow.connector.contact.stream.listener;

import lombok.RequiredArgsConstructor;
import mc.monacotelecom.workflow.connector.common.service.CommonIntegrationService;
import org.activiti.cloud.api.process.model.IntegrationRequest;
import org.springframework.cloud.stream.annotation.StreamListener;
import org.springframework.stereotype.Component;

import com.millicom.workflow.connector.contact.domain.request.CreateContactRequest;
import com.millicom.workflow.connector.contact.service.ContactsCreationService;

import static com.millicom.workflow.connector.contact.stream.channel.ContactConnectorChannels.WKF_CONTACT_CREATE_CONTACT;

import java.util.Map;


@Component
@RequiredArgsConstructor
public class CreateContactListener {

    public static final String UUID = "uuid";
    private final CommonIntegrationService commonIntegrationService;
    private final ContactsCreationService contactCreationService;

    @StreamListener(WKF_CONTACT_CREATE_CONTACT)
    public void createContacts(IntegrationRequest event) {
        var request = commonIntegrationService.getListenerInput(event, CreateContactRequest.class);

        var uuid = contactCreationService.createContact(request);
        Map<String, Object> outBoundVariable = Map.of(UUID, uuid);

        commonIntegrationService.sendResult(event, outBoundVariable);
    }
}
