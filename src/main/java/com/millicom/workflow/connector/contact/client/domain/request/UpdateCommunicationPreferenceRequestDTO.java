package com.millicom.workflow.connector.contact.client.domain.request;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Locale;

import com.millicom.workflow.connector.contact.enums.CommunicationChannelEnum;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class UpdateCommunicationPreferenceRequestDTO {
    private CommunicationChannelEnum channel;
    private Locale language;
    private String email;
    private String phoneNumber;

}
