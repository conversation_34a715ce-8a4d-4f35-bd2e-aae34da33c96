package com.millicom.workflow.connector.contact.service;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import com.millicom.workflow.connector.contact.client.ContactClient;
import com.millicom.workflow.connector.contact.domain.request.CreateContactRequest;
import com.millicom.workflow.connector.contact.exception.ContactConnectorException;

@Slf4j
@Service
@AllArgsConstructor
public class ContactsCreationService {
    private final ContactClient contactClient;

    public String createContact(CreateContactRequest request) {
        var contact = request.getContact();
        if (contact.getUuid() == null) {
            try {
                var uuid = contactClient.createContact(contact);
                request.getContact().setUuid(uuid);
            } catch (Exception e) {
                log.error(String.format("couldn't create the contact for customer %s and order %s", request.getCustomerReference(), request.getCorrelationId()));
                throw new ContactConnectorException(String.format("couldn't create the contact for customer %s and order %s", request.getCustomerReference(), request.getCorrelationId()));
            }
        }
        return contact.getUuid();
    }
}

