package com.millicom.workflow.connector.contact.enums;

public enum AccountTypeEnum {
    B2C(Constant.B2C),
    B2B(Constant.B2B);

    private final String name;

    AccountTypeEnum(String name) {
        this.name = name;
    }

    public String getName() {
        return name;
    }

    public static final class Constant {

        public static final String B2C = "B2C";
        public static final String B2B = "B2B";

        private Constant() {}
    }
}
