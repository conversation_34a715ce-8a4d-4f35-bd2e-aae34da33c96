package com.millicom.workflow.connector.contact.enums;

public enum PermissionEnum {
    ALLOW_EMAIL_CONTACT("Email"),
    ALLOW_SMS_CONTACT("SMS"),
    ALLOW_PHONE_CONTACT("Phone"),
    ALLOW_DIRECT_MAIL_CONTACT("Direct mail"),
    ALLOW_FOTS_CONTACT("FOTS");

    private String value;

    private PermissionEnum(String value) {
        this.value = value;
    }

    public String getValue() {
        return this.value;
    }
}
