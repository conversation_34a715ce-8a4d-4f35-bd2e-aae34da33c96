package com.millicom.workflow.connector.contact.client.domain.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;


import javax.validation.constraints.NotNull;

import com.millicom.workflow.connector.contact.enums.PermissionEnum;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class PermissionDTO implements Comparable<PermissionDTO> {

    @NotNull
    private PermissionEnum permission;
    private Boolean enabled;

    @Override
    public int compareTo(PermissionDTO o) {
        return permission.ordinal() - o.permission.ordinal();
    }
}
