package com.millicom.workflow.connector.contact.client.domain.request;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class UpdateAddressRequestDTO {

    private String addressLine1;
    private String addressLine2;
    private String addressLine3;
    private String town;
    private String county;
    private String code;
    private String poBox;
    private String area;
    private String country;
    private String street;
    private String streetNumber;
    private String streetQualifier;
    private String buildingType;
    private String buildingBlock;
    private String flatNumber;
}
