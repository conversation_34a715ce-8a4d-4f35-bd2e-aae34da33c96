package com.millicom.workflow.connector.contact.service;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import com.millicom.workflow.connector.contact.client.ContactClient;
import com.millicom.workflow.connector.contact.client.domain.request.UpdateAddressWithTypeRequestDTO;
import com.millicom.workflow.connector.contact.domain.dto.ContactAddressDTO;
import com.millicom.workflow.connector.contact.domain.request.UpdateInstallationAddressRequest;
import com.millicom.workflow.connector.contact.exception.ContactConnectorException;
import com.millicom.workflow.spi.com.dto.v1.enumeration.AddressTypeEnum;

@Slf4j
@Service
@AllArgsConstructor
public class UpdateInstallationAddressService {

    private static final String CHGE_ADDR = "CHGE_ADDR";

    private ContactClient contactClient;
    //#SPI_V2 this needs to be refactored
    public void callUpdateInstallationAddress(UpdateInstallationAddressRequest updateInstallationAddressRequest) {
        updateInstallationAddressRequest.getCart().getOffers().stream()
                .filter(offer -> CHGE_ADDR.equals(offer.getOfferAction()))
                .forEach(offerDTO -> {
                    var address = offerDTO.getContact().getAddresses().stream().findFirst().orElseThrow(() -> new ContactConnectorException("Could not find address to update"));
                    UpdateAddressWithTypeRequestDTO updateAddressWithTypeRequest = getUpdateAddressRequestDTO(address);
                    contactClient.updateAddress(offerDTO.getContact().getContactUuid(), updateAddressWithTypeRequest);
                });
    }

    private UpdateAddressWithTypeRequestDTO getUpdateAddressRequestDTO(ContactAddressDTO address) {
        UpdateAddressWithTypeRequestDTO updateAddressWithTypeRequest = new UpdateAddressWithTypeRequestDTO();
        updateAddressWithTypeRequest.setCode(address.getCode());
        updateAddressWithTypeRequest.setPoBox(address.getPoBox());
        updateAddressWithTypeRequest.setType(AddressTypeEnum.INSTALLATION);
        return updateAddressWithTypeRequest;
    }
}