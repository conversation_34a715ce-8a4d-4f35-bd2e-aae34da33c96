package com.millicom.workflow.connector.contact.service;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.EnumUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.millicom.workflow.connector.contact.client.ContactClient;
import com.millicom.workflow.connector.contact.client.domain.dto.CommunicationPreferenceDTO;
import com.millicom.workflow.connector.contact.client.domain.request.ContactRequestDTO;
import com.millicom.workflow.connector.contact.client.domain.request.CreateAddressRequestDTO;
import com.millicom.workflow.connector.contact.client.domain.request.EmailRequestDTO;
import com.millicom.workflow.connector.contact.client.domain.request.IdentityDocumentRequestDTO;
import com.millicom.workflow.connector.contact.client.domain.request.PhoneNumberRequestDTO;
import com.millicom.workflow.connector.contact.domain.dto.CustomerCommunicationSettingsDTO;
import com.millicom.workflow.connector.contact.domain.request.CreateContactEmailRequest;
import com.millicom.workflow.connector.contact.domain.request.CreateContactsRequest;
import com.millicom.workflow.connector.contact.domain.response.CustomerContactsResponse;
import com.millicom.workflow.connector.contact.enums.CommunicationChannelEnum;
import com.millicom.workflow.connector.contact.enums.EmailTypeEnum;
import com.millicom.workflow.connector.contact.exception.ContactConnectorException;
import com.millicom.workflow.connector.contact.mappers.AddressMapper;
import com.millicom.workflow.spi.com.dto.v1.ContactDTO;
import com.millicom.workflow.spi.com.dto.v1.IdentityDocumentDTO;
import com.millicom.workflow.spi.com.dto.v1.enumeration.AddressTypeEnum;

import static com.millicom.workflow.connector.contact.mappers.ContactEmailMapper.toEmailRequestList;
import static com.millicom.workflow.connector.contact.mappers.ContactPhoneNumberMapper.toContactPhoneNumberDTOList;
import static com.millicom.workflow.connector.contact.mappers.IdentityDocumentsMapper.toIdentityDocumentDTOList;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Locale;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Service
@AllArgsConstructor
public class ContactCreationService {

    public static final String MOBILE = "MOBILE";
    private final ContactClient contactClient;

    public CustomerContactsResponse createContact(CreateContactsRequest createContactsRequest) {

        var customerContactsResponse = new CustomerContactsResponse();
        customerContactsResponse.setContacts(createContactsRequest.getContacts());
        List<String> errorMessages = new ArrayList<>();

        for (ContactDTO contact : createContactsRequest.getContacts()) {
                if (contact != null && contact.getCreatedAt() == null) {
    ContactRequestDTO createContactRequestDTO = null;
    try {
        createContactRequestDTO = getCreateContactRequest(contact);
        createContactRequestDTO.setContactType(contact.getContactType());

        if ("OWNER".equals(contact.getContactType())) {
            createContactRequestDTO.setUuid(createContactsRequest.getUuid());
            contact.setUuid(createContactsRequest.getUuid());
        } else if ("PAYER".equals(contact.getContactType())) {
            ContactDTO owner = createContactsRequest.getContacts().stream()
                    .filter(c -> "OWNER".equals(c.getContactType()))
                    .findFirst()
                    .orElse(null);

            if (areSamePerson(owner, contact)) {
                createContactRequestDTO.setUuid(owner.getUuid());
                contact.setUuid(createContactsRequest.getUuid());
                contact.setCreatedAt(owner.getCreatedAt());
            }
            continue;
        } else {
            createContactRequestDTO.setUuid(contact.getUuid());
        }

        log.debug("🟡 Intentando crear contacto...");
        log.debug("ContactType: {}", contact.getContactType());
        log.debug("UUID: {}", createContactRequestDTO.getUuid());
        log.debug("FirstName: {}", createContactRequestDTO.getFirstName());
        log.debug("LastName: {}", createContactRequestDTO.getLastName());
        log.debug("Emails: {}", createContactRequestDTO.getEmails());
        log.debug("Phones: {}", createContactRequestDTO.getPhoneNumbers());
        log.debug("Documents: {}", createContactRequestDTO.getIdentityDocuments());
        log.debug("Addresses: {}", createContactRequestDTO.getAddresses());

        contactClient.createContact(createContactRequestDTO);

        contact.setCreatedAt(LocalDateTime.now());
    } catch (Exception e) {
        errorMessages.add(String.format(
            "couldn't create the %s account for customer %s and order %s",
            contact.getContactType(),
            createContactsRequest.getCustomerReference(),
            createContactsRequest.getCorrelationId()
        ));
        log.error("Error al crear contacto tipo {} para customer {} y order {}. Datos enviados: {}",
            contact.getContactType(),
            createContactsRequest.getCustomerReference(),
            createContactsRequest.getCorrelationId(),
            createContactRequestDTO,
            e);
    }
}           
        }

        if (!CollectionUtils.isEmpty(errorMessages)) {
            throw new ContactConnectorException(String.join(",\n", errorMessages));
        }
        return customerContactsResponse;
    }

    public ContactRequestDTO getCreateContactRequest(ContactDTO contactDTO) {
        List<EmailRequestDTO> emails = toEmailRequestList(contactDTO.getEmails()).stream()
                .filter(email -> StringUtils.isNotBlank(email.getEmail()))
                .collect(Collectors.toList());

        List<PhoneNumberRequestDTO> phoneNumbers = toContactPhoneNumberDTOList(contactDTO.getPhoneNumbers()).stream()
                .filter(number -> StringUtils.isNotBlank(number.getPhoneNumber()))
                .collect(Collectors.toList());

        List<CreateAddressRequestDTO> addresses = new ArrayList<>();
        if (contactDTO.getAddresses() != null && !contactDTO.getAddresses().isEmpty()) {
            addresses = contactDTO.getAddresses().stream()
                    //.filter(address -> address.getType() != AddressTypeEnum.INSTALLATION)
                    .map(AddressMapper::toCreateRequest)
                    .collect(Collectors.toList());
        }

        // PermissionGroupListRequestDTO permissionGroupList =
        // getPermissionGroupListRequest(contactDTO, true);

        List<IdentityDocumentRequestDTO> identityDocuments = toIdentityDocumentDTOList(
                contactDTO.getIdentityDocuments());

        return ContactRequestDTO.builder()
                .uuid(contactDTO.getUuid())
                .emails(emails)
                .phoneNumbers(phoneNumbers)
                .identityDocuments(identityDocuments)
                .firstName(contactDTO.getFirstName())
                .lastName(contactDTO.getLastName())
                .addresses(addresses)
                .gdprValidationDate(contactDTO.getGdprValidationDate())
                .gdprLanguage(Objects.nonNull(contactDTO.getGdprLanguage()) ? contactDTO.getGdprLanguage()
                        : Locale.getDefault())
                .allowThirdParties(contactDTO.getAllowThirdParties())
                .build();
    }

    /*
     * public PermissionGroupListRequestDTO getPermissionGroupListRequest(ContactDTO
     * contactDTO, boolean isEnabled) {
     * List<PermissionGroupRequestDTO> permissionGroups =
     * getPermissionGroups(contactDTO, isEnabled);
     * 
     * PermissionGroupListRequestDTO permissionGroupList = new
     * PermissionGroupListRequestDTO();
     * permissionGroupList.setPermissionGroups(permissionGroups);
     * permissionGroupList.setAllowThirdParty(contactDTO.getAllowThirdParties());
     * permissionGroupList.setHasMarketingProfiling(contactDTO.
     * getHasMarketingProfiling());
     * return permissionGroupList;
     * }
     * 
     * public List<PermissionGroupRequestDTO> getPermissionGroups(ContactDTO
     * contactDTO, boolean isEnabled) {
     * List<PermissionGroupRequestDTO> permissionGroups = new ArrayList<>();
     * contactDTO.getPermissions().forEach((permissionGroupEnum, permissionEnums) ->
     * {
     * PermissionGroupRequestDTO permissionGroupRequestDTO = new
     * PermissionGroupRequestDTO();
     * permissionGroupRequestDTO.setPermissionGroup(permissionGroupEnum);
     * permissionEnums.forEach(permissionEnum ->
     * permissionGroupRequestDTO.getPermissions()
     * .add(new PermissionRequestDTO(permissionEnum, isEnabled)));
     * permissionGroups.add(permissionGroupRequestDTO);
     * });
     * return permissionGroups;
     * }
     */

    /*
     * public CommunicationPreferenceDTO
     * getDefaultCommunicationSettings(CreateContactsRequest createContactsRequest)
     * {
     * if (Objects.nonNull(createContactsRequest.getCommunicationSettings())
     * && !StringUtils.isEmpty(createContactsRequest.getCommunicationSettings().
     * getPreferredChannel())
     * && !StringUtils.isEmpty(createContactsRequest.getCommunicationSettings().
     * getLanguage())) {
     * return
     * getCommunicationSettings(createContactsRequest.getCommunicationSettings(),
     * createContactsRequest.getCommunicationSettings().getLanguage());
     * } else {
     * return null;
     * }
     * }
     * 
     * public CommunicationPreferenceDTO
     * getPayerCommunicationSettings(CreateContactsRequest createContactsRequest) {
     * if (Objects.nonNull(createContactsRequest.getCommunicationSettings())
     * && !StringUtils.isEmpty(createContactsRequest.getCommunicationSettings().
     * getPreferredChannel())
     * && !StringUtils.isEmpty(createContactsRequest.getCommunicationSettings().
     * getLanguage())) {
     * return
     * getCommunicationSettings(createContactsRequest.getCommunicationSettings(),
     * createContactsRequest.getInvoiceSettings().getLanguage());
     * } else {
     * return null;
     * }
     * }
     */

    public CommunicationPreferenceDTO getCommunicationSettings(CustomerCommunicationSettingsDTO settings,
            String communicationLanguage) {
        CommunicationPreferenceDTO communicationPreferenceDTO = new CommunicationPreferenceDTO();
        String commChannel = settings.getPreferredChannel();

        if (MOBILE.equals(commChannel)) {
            commChannel = CommunicationChannelEnum.MOBILE_NUMBER.name();
        }
        if (StringUtils.isNotBlank(commChannel) && EnumUtils.isValidEnum(CommunicationChannelEnum.class, commChannel)) {
            communicationPreferenceDTO.setChannel(CommunicationChannelEnum.valueOf(commChannel));
        }

        if (settings.getEmail() != null) {
            communicationPreferenceDTO.setEmail(StringUtils.trimToNull(settings.getEmail()));
        }
        communicationPreferenceDTO.setLanguage(Locale.forLanguageTag(communicationLanguage));
        communicationPreferenceDTO.setPhoneNumber(StringUtils.trimToNull(settings.getNumber()));
        return communicationPreferenceDTO;
    }

    public boolean areSamePerson(ContactDTO owner, ContactDTO payer) {
        if (owner == null || payer == null)
            return false;

        IdentityDocumentDTO ownerMainIdDoc = owner.getIdentityDocuments().stream()
                .filter(o -> o.getMainIdentityDocument() == true)
                .findFirst()
                .orElse(null);

        IdentityDocumentDTO payerMainIdDoc = payer.getIdentityDocuments().stream()
                .filter(o -> o.getMainIdentityDocument() == true)
                .findFirst()
                .orElse(null);

        return ownerMainIdDoc != null && ownerMainIdDoc.getIdentifier().equals(payerMainIdDoc.getIdentifier());
    }

    public void callCreateContactEmail(CreateContactEmailRequest createContactEmailRequest) {
        if (createContactEmailRequest.getEmail() == null)
            return;

        var emailRequest = EmailRequestDTO.builder()
                .type(EmailTypeEnum.MAIN.name())
                .validated(false)
                .email(createContactEmailRequest.getEmail()).build();

        this.contactClient.createContactEmail(createContactEmailRequest.getContactUuid(), emailRequest);
    }
}
