package com.millicom.workflow.connector.contact.client.domain.request;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateDeserializer;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.millicom.workflow.connector.contact.enums.TitleEnum;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;


import javax.validation.constraints.PastOrPresent;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Locale;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class PatchContactRequestDTO {

    private String firstName;

    private String lastName;

    private TitleEnum title;

    @JsonDeserialize(using = LocalDateDeserializer.class)
    private LocalDate birthDate;

    private String nationality;

    private String companyPosition;

    private Boolean allowThirdParties;

    @PastOrPresent
    @JsonDeserialize(using = LocalDateDeserializer.class)
    private LocalDate gdprValidatedAt;

    private Locale gdprLanguage;

    private Boolean vip;

    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime idLastCheckedAt;
}
