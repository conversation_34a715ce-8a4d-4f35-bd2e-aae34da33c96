package com.millicom.workflow.connector.contact.domain.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.validation.constraints.Email;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class CustomerCommunicationSettingsDTO {

    @Email
    private String email;
    private String number;
    private String preferredChannel;
    private String country;
    private String language;
}
