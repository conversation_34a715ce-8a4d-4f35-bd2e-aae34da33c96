package com.millicom.workflow.connector.contact.mappers;

import com.millicom.workflow.connector.contact.client.domain.dto.CommunicationPreferenceDTO;
import com.millicom.workflow.connector.contact.client.domain.response.ContactResponseDTO;
import com.millicom.workflow.connector.contact.domain.dto.CustomerCommunicationSettingsDTO;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class CommunicationSettingsMapper {

    public static CustomerCommunicationSettingsDTO toCommunicationSettings(CommunicationPreferenceDTO communicationPreference){
        return CustomerCommunicationSettingsDTO.builder()
                .email(communicationPreference.getEmail())
                .number(communicationPreference.getPhoneNumber())
                .language(communicationPreference.getLanguage().toString())
                .preferredChannel(communicationPreference.getChannel().name())
                .build();
    }
}
