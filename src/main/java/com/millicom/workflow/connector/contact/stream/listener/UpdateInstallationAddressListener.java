package com.millicom.workflow.connector.contact.stream.listener;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import mc.monacotelecom.workflow.connector.common.service.CommonIntegrationService;

import static com.millicom.workflow.connector.contact.stream.channel.ContactConnectorChannels.WKF_CONTACT_UPDATE_INSTALLATION_ADDRESS;

import org.activiti.cloud.api.process.model.IntegrationRequest;
import org.springframework.cloud.stream.annotation.StreamListener;
import org.springframework.stereotype.Component;

import com.millicom.workflow.connector.contact.domain.request.UpdateInstallationAddressRequest;
import com.millicom.workflow.connector.contact.service.UpdateInstallationAddressService;

@Slf4j
@Component
@RequiredArgsConstructor
public class UpdateInstallationAddressListener {

    private final UpdateInstallationAddressService updateInstallationAddressService;
    private final CommonIntegrationService commonIntegrationService;

    // #SPI_V2 #EPIC is this still needed?
    @StreamListener(WKF_CONTACT_UPDATE_INSTALLATION_ADDRESS)
    public void onUpdateInstallationAddress(IntegrationRequest event) {
        var request = commonIntegrationService.getListenerInput(event, UpdateInstallationAddressRequest.class);

        updateInstallationAddressService.callUpdateInstallationAddress(request);

        commonIntegrationService.sendResult(event);
    }
}