package com.millicom.workflow.connector.contact.stream.listener;

import lombok.RequiredArgsConstructor;
import mc.monacotelecom.workflow.connector.common.service.CommonIntegrationService;
import org.activiti.cloud.api.process.model.IntegrationRequest;
import org.springframework.cloud.stream.annotation.StreamListener;
import org.springframework.stereotype.Component;

import com.millicom.workflow.connector.contact.domain.request.UpdateCommunicationSettingsRequest;
import com.millicom.workflow.connector.contact.service.UpdateCommunicationSettingsService;

import static com.millicom.workflow.connector.contact.stream.channel.ContactConnectorChannels.WKF_CONTACT_UPDATE_COMMUNICATION_SETTINGS;

import java.util.Map;

@Component
@RequiredArgsConstructor
public class UpdateCommunicationSettingsListener {

    private final CommonIntegrationService commonIntegrationService;
    private final UpdateCommunicationSettingsService updateCommunicationSettingsService;

    @StreamListener(WKF_CONTACT_UPDATE_COMMUNICATION_SETTINGS)
    public void onUpdateCommunicationSettings(IntegrationRequest event) {
        var request = commonIntegrationService.getListenerInput(event, UpdateCommunicationSettingsRequest.class);

        var communicationSettings = updateCommunicationSettingsService.callUpdateCommunicationSettings(request);
        Map<String, Object> outBoundVariable = Map.of("communicationSettings", communicationSettings);

        commonIntegrationService.sendResult(event, outBoundVariable);
    }
}