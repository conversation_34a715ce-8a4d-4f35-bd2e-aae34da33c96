package com.millicom.workflow.connector.contact.stream.listener;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import mc.monacotelecom.workflow.connector.common.service.CommonIntegrationService;
import org.activiti.cloud.api.process.model.IntegrationRequest;
import org.springframework.cloud.stream.annotation.StreamListener;
import org.springframework.stereotype.Component;

import com.millicom.workflow.connector.contact.domain.request.ValidateEmailRequest;
import com.millicom.workflow.connector.contact.service.ValidateEmailService;

import static com.millicom.workflow.connector.contact.stream.channel.ContactConnectorChannels.WKF_CONTACT_VALIDATE_EMAIL;

import java.util.Map;

@Slf4j
@Component
@RequiredArgsConstructor
public class ValidateEmailListener {

    private final CommonIntegrationService commonIntegrationService;
    private final ValidateEmailService validateEmailService;

    // #SPI_V2 #EPIC is this still needed? Logic should be reviewed to validate one email for a contactUuid, and call this task multiple times for each emails we have to validate at BPMN level?
    @StreamListener(WKF_CONTACT_VALIDATE_EMAIL)
    public void onUpdateInstallationAddress(IntegrationRequest event) {
        var request = commonIntegrationService.getListenerInput(event, ValidateEmailRequest.class);

        var validateEmailResponse = validateEmailService.callValidateEmail(request);
        Map<String, Object> outBoundVariable = Map.of("validateEmailResponse", validateEmailResponse);

        commonIntegrationService.sendResult(event, outBoundVariable);
    }
}
