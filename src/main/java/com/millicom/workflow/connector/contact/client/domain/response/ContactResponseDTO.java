package com.millicom.workflow.connector.contact.client.domain.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

import com.millicom.workflow.connector.contact.client.domain.dto.CommunicationPreferenceDTO;
import com.millicom.workflow.connector.contact.client.domain.response.permissions.PermissionGroupListResponseDTO;


@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ContactResponseDTO {

    private String uuid;
    private String firstName;
    private String lastName;
    private String title;
    private LocalDate birthDate;
    private String nationality;
    private String companyPosition;
    private Boolean allowThirdParties;
    private LocalDate gdprValidatedAt;
    private String gdprLanguage;
    private Boolean vip;
    private LocalDateTime idLastCheckedAt;
    private List<EmailResponseDTO> emails;
    private List<PhoneNumberResponseDTO> phoneNumbers;
    private List<AddressResponseDTO> addresses;
    private List<IdentityDocumentResponseDTO> identityDocuments;
    private CommunicationPreferenceDTO communicationPreference;
    private PermissionGroupListResponseDTO permissions;
    private LocalDateTime createdAt;
}
