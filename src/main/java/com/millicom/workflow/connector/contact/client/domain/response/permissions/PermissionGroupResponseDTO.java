package com.millicom.workflow.connector.contact.client.domain.response.permissions;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.millicom.workflow.connector.contact.enums.PermissionGroupEnum;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;


import java.util.ArrayList;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class PermissionGroupResponseDTO {

    private String name;
    private PermissionGroupEnum permissionGroup;

    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private List<PermissionResponseDTO> permissions = new ArrayList<>();
}
