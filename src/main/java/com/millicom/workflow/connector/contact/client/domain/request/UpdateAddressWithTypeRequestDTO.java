package com.millicom.workflow.connector.contact.client.domain.request;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;


import javax.validation.constraints.NotNull;

import com.millicom.workflow.spi.com.dto.v1.enumeration.AddressTypeEnum;

@AllArgsConstructor
@NoArgsConstructor
@Data
public class UpdateAddressWithTypeRequestDTO extends UpdateAddressRequestDTO {

    @NotNull
    private AddressTypeEnum type;
}