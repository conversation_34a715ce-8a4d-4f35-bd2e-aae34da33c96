package com.millicom.workflow.connector.contact.stream.listener;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import mc.monacotelecom.workflow.connector.common.service.CommonIntegrationService;
import org.activiti.cloud.api.process.model.IntegrationRequest;
import org.springframework.cloud.stream.annotation.StreamListener;
import org.springframework.stereotype.Component;

import com.millicom.workflow.connector.contact.domain.request.CreateContactEmailRequest;
import com.millicom.workflow.connector.contact.domain.request.CreateContactsRequest;
import com.millicom.workflow.connector.contact.domain.request.FillSomOfferFromContactRequest;
import com.millicom.workflow.connector.contact.domain.request.GetAccountContactEvent;
import com.millicom.workflow.connector.contact.service.ContactCreationService;
import com.millicom.workflow.connector.contact.service.ContactRetrieveService;

import static com.millicom.workflow.connector.contact.stream.channel.ContactConnectorChannels.WKF_CONTACT_CREATE_CONTACTS;
import static com.millicom.workflow.connector.contact.stream.channel.ContactConnectorChannels.WKF_CONTACT_CREATE_EMAIL_CONTACTS;
import static com.millicom.workflow.connector.contact.stream.channel.ContactConnectorChannels.WKF_CONTACT_GETACCOUNTCONTACTS;
import static com.millicom.workflow.connector.contact.stream.channel.ContactConnectorChannels.WKF_CONTACT_GETCONTACT;

import java.util.Map;

@Component
@RequiredArgsConstructor
@Slf4j
public class ContactListener {

    public static final String CUSTOMER_CONTACTS_RESPONSE = "customerContacts";
    private final CommonIntegrationService commonIntegrationService;
    private final ContactRetrieveService contactRetrieveService;
    private final ContactCreationService contactCreationService;

    @StreamListener(WKF_CONTACT_CREATE_CONTACTS)
    public void createContact(IntegrationRequest event) {
        var request = commonIntegrationService.getListenerInput(event, CreateContactsRequest.class);

        var customerContactsResponse = contactCreationService.createContact(request);
        Map<String, Object> outBoundVariable = Map.of(CUSTOMER_CONTACTS_RESPONSE, customerContactsResponse);

        commonIntegrationService.sendResult(event, outBoundVariable);
    }

    @StreamListener(WKF_CONTACT_CREATE_EMAIL_CONTACTS)
    public void createContactEmail(IntegrationRequest event) {
        var request = commonIntegrationService.getListenerInput(event, CreateContactEmailRequest.class);

        contactCreationService.callCreateContactEmail(request);

        commonIntegrationService.sendResult(event);
    }

    @StreamListener(WKF_CONTACT_GETCONTACT)
    public void getContact(IntegrationRequest event) {
        var request = commonIntegrationService.getListenerInput(event, FillSomOfferFromContactRequest.class);

        var contact = contactRetrieveService.getContact(request);
        Map<String, Object> outBoundVariable = Map.of("contact", contact);

        commonIntegrationService.sendResult(event, outBoundVariable);
    }

    // To move in account workflow connector
    @StreamListener(WKF_CONTACT_GETACCOUNTCONTACTS)
    public void getAccountContacts(IntegrationRequest event) {
        var request = commonIntegrationService.getListenerInput(event, GetAccountContactEvent.class);

        var customerContactsResponse = contactRetrieveService.getContacts(request.getAccountId());
        Map<String, Object> outBoundVariable = Map.of(CUSTOMER_CONTACTS_RESPONSE, customerContactsResponse);

        commonIntegrationService.sendResult(event, outBoundVariable);
    }
}
