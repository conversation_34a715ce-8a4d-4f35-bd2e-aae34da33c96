package com.millicom.workflow.connector.contact.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import com.millicom.workflow.connector.contact.client.AccountClient;
import com.millicom.workflow.connector.contact.client.ContactClient;
import com.millicom.workflow.connector.contact.client.domain.response.AccountContactResponseDTO;
import com.millicom.workflow.connector.contact.client.domain.response.ContactResponseDTO;
import com.millicom.workflow.connector.contact.client.domain.response.EmailResponseDTO;
import com.millicom.workflow.connector.contact.client.domain.response.PhoneNumberResponseDTO;
import com.millicom.workflow.connector.contact.domain.dto.CustomerCommunicationSettingsDTO;
import com.millicom.workflow.connector.contact.domain.request.FillSomOfferFromContactRequest;
import com.millicom.workflow.connector.contact.domain.response.CustomerContactsResponse;
import com.millicom.workflow.connector.contact.enums.ContactType;
import com.millicom.workflow.connector.contact.enums.EmailTypeEnum;
import com.millicom.workflow.connector.contact.exception.ContactConnectorException;

import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
public class ContactRetrieveService {

    private final ContactClient contactClient;
    private final AccountClient accountClient;

    private static final String DEFAULT_LANGUAGE = "en";
    private static final String DEFAULT_COUNTRY = "IE";

    public ContactResponseDTO getContact(FillSomOfferFromContactRequest fillSomOfferFromContactRequest) {
        var contactUuid = fillSomOfferFromContactRequest.getContactUuid();
        return contactClient.getContact(contactUuid);
    }

    public CustomerContactsResponse getContacts(String accountId) {
        var contacts = accountClient.getAccountContacts(accountId);

        var ownerContact = getContact(ContactType.OWNER, contacts);
        var payerContact = getContact(ContactType.PAYER, contacts);

        var customerContactsResponse = new CustomerContactsResponse();
        customerContactsResponse.setOwner(ownerContact);
        customerContactsResponse.setPayer(payerContact);
        customerContactsResponse.setCommunicationSettings(getCommunicationSettings(ownerContact));

        return customerContactsResponse;
    }

    private CustomerCommunicationSettingsDTO getCommunicationSettings(ContactResponseDTO ownerContact) {
        var email = ownerContact.getEmails().stream().filter(e -> EmailTypeEnum.MAIN.equals(e.getType())).findAny().map(EmailResponseDTO::getEmail)
                .orElseThrow(() -> new ContactConnectorException(String.format("No email found for contact uuid %s", ownerContact.getUuid())));
        var phoneNumber = ownerContact.getPhoneNumbers().stream().findFirst().map(PhoneNumberResponseDTO::getPhoneNumber)
                .orElseThrow(() -> new ContactConnectorException(String.format("No phoneNumber found for contact uuid %s", ownerContact.getUuid())));

        return new CustomerCommunicationSettingsDTO(email, phoneNumber, "", DEFAULT_COUNTRY, DEFAULT_LANGUAGE);
    }

    private ContactResponseDTO getContact(ContactType type, List<AccountContactResponseDTO> contacts) {
        var contactUuid = contacts.stream()
                .filter(c -> type.name().equals(c.getType()))
                .findFirst()
                .map(AccountContactResponseDTO::getUuid)
                .orElseThrow(() -> new ContactConnectorException(String.format("No %s contact", type.name())));

        return contactClient.getContact(contactUuid);
    }
}
