package com.millicom.workflow.connector.contact.client.domain.request;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

import com.millicom.workflow.connector.contact.enums.PermissionGroupEnum;

import java.util.ArrayList;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class PermissionGroupRequestDTO {

    @NotNull
    private PermissionGroupEnum permissionGroup;
    private List<PermissionRequestDTO> permissions = new ArrayList<>();
}
