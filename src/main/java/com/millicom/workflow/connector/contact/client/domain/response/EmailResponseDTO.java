package com.millicom.workflow.connector.contact.client.domain.response;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

import com.millicom.workflow.connector.contact.enums.EmailTypeEnum;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class EmailResponseDTO {

    private Long id;
    private String email;
    private EmailTypeEnum type;
    private Boolean validated;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
}
