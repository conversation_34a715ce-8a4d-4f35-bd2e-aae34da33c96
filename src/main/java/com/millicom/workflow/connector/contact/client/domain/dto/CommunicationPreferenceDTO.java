package com.millicom.workflow.connector.contact.client.domain.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Email;
import javax.validation.constraints.NotNull;

import com.millicom.workflow.connector.contact.enums.CommunicationChannelEnum;

import java.util.Locale;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class CommunicationPreferenceDTO {
    @NotNull
    private CommunicationChannelEnum channel;

    @NotNull
    private Locale language;

    @Email
    private String email;

    private String phoneNumber;
}
