package com.millicom.workflow.connector.contact.mappers;

import com.millicom.workflow.connector.contact.domain.request.EnrichContactRequest;
import com.millicom.workflow.connector.contact.domain.response.CustomerResponse;
import com.millicom.workflow.connector.contact.domain.response.EnrichContactResponse;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class EnrichContactMapper {

    public static EnrichContactResponse toEnrichContactResponse(EnrichContactRequest enrichContactRequest){
        return EnrichContactResponse.builder()
                .customer(toCustomerResponse(enrichContactRequest))
                .build();
    }
    private static CustomerResponse toCustomerResponse(EnrichContactRequest enrichContactRequest){
        return CustomerResponse.builder()
                .payer(enrichContactRequest.getPayer())
                .owner(enrichContactRequest.getOwner())
                .build();
    }
}
