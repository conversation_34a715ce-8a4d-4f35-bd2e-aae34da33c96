# ===================== Project =====================
sonar.projectKey=galaxion_projects_millicom_backend_workflow-engine_connectors_contact-workflow-connector
sonar.projectName=contact-workflow-connector
sonar.projectVersion=1.0

# ===================== Resources and TESTS =====================
sonar.sources=src/main/java
sonar.tests=src/test/java
sonar.language=java
sonar.sourceEncoding=UTF-8
sonar.java.binaries=target/classes

# ===================== Coverage =====================
sonar.coverage.jacoco.xmlReportPaths=target/site/jacoco/jacoco.xml

# Exclude the rest of the components
sonar.coverage.exclusions=**/dto/**, **/config/**, **/*Configuration.java, **/exception/**, **/mappers/**, **/constants/**
sonar.exclusions=**/dto/**, **/config/**, **/*Configuration.java, **/exception/**, **/mappers/**, **/constants/**

#Includes changes of AddressMapper.java
sonar.inclusions=**/mappers/AddressMapper.java

# ===================== Quality =====================
sonar.newCode.period=previous_version
