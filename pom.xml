<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xmlns="http://maven.apache.org/POM/4.0.0"
    xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>mc.monacotelecom.workflow</groupId>
        <artifactId>workflow-connector-parent-pom</artifactId>
        <version>3.1.13</version>
    </parent>

    <artifactId>contact-workflow-connector</artifactId>
    <version>1.0.2-SNAPSHOT</version>

    <properties>
        <feign.version>11.7</feign.version>
        <workflow-connector-dependencies.version>3.1.13</workflow-connector-dependencies.version>
        <project.name>contact-workflow-connector</project.name>
        <sonar.login>****************************************</sonar.login>
        <sonar.projectKey>${project.artifactId}</sonar.projectKey>
        <sonar.qualitygate.wait>true</sonar.qualitygate.wait>
        <docker.image.name>contact-workflow-connector</docker.image.name>
        <docker.image.path>galaxion</docker.image.path>
        <docker.image.from>galaxion-java-11:latest</docker.image.from>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <artifactId>workflow-connector-deployer</artifactId>
                <groupId>millicom.tigo.workflow</groupId>
                <version>1.0.0</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <dependencies>
        <dependency>
            <groupId>millicom.tigo.workflowengine</groupId>
            <artifactId>workflow-spi-tigo-co</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>
    </dependencies>
</project>