global:
  name: contact-workflow-connector
  description: Contact Workflow Connector
  maintainers:
    - email: j<PERSON><PERSON>.be<PERSON><PERSON>@risf.io
      name: <PERSON>
    - email: <EMAIL>
      name: Lu<PERSON>vic Cuny
  labels:
    team: risf

main:
  replicas: 1
  securityContext: disabled
  image:
    pullPolicy: Always
    repository: nexus-galaxion-qa.tigo.com.gt:5005/galaxion/workflow-engine/contact-workflow-connector-develop
    tag: auto
  port: 8080
  alivePath: /actuator/health
  readyPath: /actuator/health
  labels:
    owner: risf
  resources:
    limits:
      cpu: "500m"
      memory: "1Gi"
    requests:
      cpu: "500m"
      memory: "1Gi"

configuration:
  envSecret:
    SPRING_RABBITMQ_USERNAME: TO_BE_DEFINED
    SPRING_RABBITMQ_PASSWORD: TO_BE_DEFINED
  env:
    ### SERVER ###
    JAVA_TOOL_OPTIONS: -Xms1024m -Xmx1024m
    SERVER_PORT: 8080
    ### SPRING ###
    SPRING_PROFILES_ACTIVE: output-logs-as-json
    ### RABBITMQ ###
    SPRING_RABBITMQ_HOST: TO_BE_DEFINED
    SPRING_RABBITMQ_PORT: TO_BE_DEFINED
    ### WEBSERVICES URLS ###
    WEBSERVICE_CONTACT_URL: contacts-service:8080
    WEBSERVICE_ACCOUNT_URL: accounts-service:8080
