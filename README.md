# Contact Connector

This microservice serves as a connector between the flow of workflow-engine and the contact microservice.

### Build local jib image

```
mvn com.google.cloud.tools:jib-maven-plugin:dockerBuild -Dartifact=container -pl contact-workflow-connector-webservice -Djib.to.image=localhost:5000/contact-workflow-connector:latest
```

Workflow-engine is using RabbitMQ messages to forward requests to this microservice, which in turn it contacts
contact to retrieve the required information.

## RabbitMQ queues

The queues handled by this connector are:

- wkf_contact_create_contacts
- wkf_contact_validate_email
- wkf_contact_enrich_contact
- wkf_contact_update_installation_address
- wkf_contact_update_communication_settings
- wkf_contact_forget_Email
- wkf_contact_forget_address
- wkf_contact_forget_contacts
- wkf_contact_forget_comm_settings

## Prerequisites

In order for this application to work, you require to have a RabbitMQ
docker images running

Keep in mind that we need to export specific ports in order for the application
to communicate with the docker image

### Run RabbitMQ

```bash
docker pull rabbitmq
docker run -d --hostname rabbit --name rabbit -p 15672:15672 -p 5672:5672 rabbitmq:3-management
```

> Here we can change the port forwarding as we desire. Forwared ports should be configured
> in [application.properties](./workflow-connectors\contact-workflow-connector/src/main/resources/application.properties)
> or in [local.env](./envs/local.env)

## How to Start

Since it is a standard Spring boot application, we can start it same as the rest of applications
> make sure to add the env file in the `Run Configuration`

## How to test

In order to test the connector we need to mock the rabbitmq messages.
This can be done with the [workflow-mock](https://git.itsf.io/galaxion/oss-monaco/workflow-engine/workflow-mock/-/tree/master) project

In order to find out how to initialise workflow-mock please refer to its [README file](https://git.itsf.io/galaxion/oss-monaco/workflow-engine/workflow-mock/-/blob/master/README.md)

## Dependencies

- [RabbitMQ](https://hub.docker.com/_/rabbitmq/)
